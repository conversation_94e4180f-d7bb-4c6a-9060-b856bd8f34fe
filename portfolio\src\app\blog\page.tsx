import { Metadata } from 'next';
import { getBlogPosts, getFeaturedBlogPosts, formatDate } from '@/lib/api';
import Footer from '@/components/Footer';
import ScrollToTop from '@/components/ScrollToTop';
import BlogPageClient from './BlogPageClient';

export const metadata: Metadata = {
  title: 'Blog - Insights & Tips',
  description: 'Video editing tutorials, tips, and insights from professional video editor Uttam Rimal.',
  openGraph: {
    title: 'Blog - Uttam Rimal',
    description: 'Video editing tutorials, tips, and insights from professional video editor Uttam Rimal.',
  },
};

export default async function BlogPage() {
  // Fetch data on the server side
  const [allPosts, featuredPosts] = await Promise.all([
    getBlogPosts(),
    getFeaturedBlogPosts(),
  ]);

  return (
    <main>
      <BlogPageClient allPosts={allPosts} featuredPosts={featuredPosts} />
      <Footer />
      <ScrollToTop />
    </main>
  );
}
