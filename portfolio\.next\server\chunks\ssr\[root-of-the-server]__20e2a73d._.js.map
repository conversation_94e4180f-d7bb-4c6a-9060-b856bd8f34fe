{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/WhatsAppButton.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { MessageCircle } from 'lucide-react';\n\nexport default function WhatsAppButton() {\n  const [isVisible, setIsVisible] = useState(false);\n  const [isHovered, setIsHovered] = useState(false);\n  const [showTooltip, setShowTooltip] = useState(false);\n\n  // Your WhatsApp number (replace with actual number)\n  const whatsappNumber = '+977XXXXXXXXXX'; // Replace with your actual WhatsApp number\n  const message = encodeURIComponent('Hi! I found your portfolio and would like to discuss a video editing project.');\n\n  useEffect(() => {\n    const toggleVisibility = () => {\n      if (window.pageYOffset > 300) {\n        setIsVisible(true);\n      } else {\n        setIsVisible(false);\n      }\n    };\n\n    window.addEventListener('scroll', toggleVisibility);\n    return () => window.removeEventListener('scroll', toggleVisibility);\n  }, []);\n\n  useEffect(() => {\n    // Show tooltip after 3 seconds of being visible\n    if (isVisible) {\n      const timer = setTimeout(() => {\n        setShowTooltip(true);\n        // Hide tooltip after 5 seconds\n        setTimeout(() => setShowTooltip(false), 5000);\n      }, 3000);\n\n      return () => clearTimeout(timer);\n    }\n  }, [isVisible]);\n\n  const handleWhatsAppClick = () => {\n    const whatsappUrl = `https://wa.me/${whatsappNumber.replace(/[^0-9]/g, '')}?text=${message}`;\n    window.open(whatsappUrl, '_blank', 'noopener,noreferrer');\n  };\n\n  if (!isVisible) {\n    return null;\n  }\n\n  return (\n    <div className=\"fixed bottom-24 right-8 z-50\">\n      {/* Auto-show Tooltip */}\n      <div className={`\n        absolute right-16 top-1/2 -translate-y-1/2 bg-accent text-accent-foreground px-4 py-3 rounded-lg text-sm whitespace-nowrap\n        transition-all duration-500 pointer-events-none shadow-lg\n        ${showTooltip && !isHovered ? 'opacity-100 translate-x-0 scale-100' : 'opacity-0 translate-x-4 scale-95'}\n      `}>\n        💬 Need help with video editing?\n        <div className=\"absolute top-1/2 -translate-y-1/2 -right-1 w-2 h-2 bg-accent rotate-45\"></div>\n      </div>\n\n      <button\n        onClick={handleWhatsAppClick}\n        onMouseEnter={() => {\n          setIsHovered(true);\n          setShowTooltip(false);\n        }}\n        onMouseLeave={() => setIsHovered(false)}\n        className={`group transition-all duration-300 ${\n          isVisible ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'\n        }`}\n        aria-label=\"Contact via WhatsApp\"\n      >\n        {/* Main WhatsApp Button */}\n        <div className=\"relative\">\n          {/* Ripple Effect Background */}\n          <div className=\"absolute inset-0 rounded-full bg-accent/30 animate-ping\"></div>\n          <div className=\"absolute inset-0 rounded-full bg-accent/20 animate-ping animation-delay-1000\"></div>\n\n          <div className={`\n            relative bg-accent hover:bg-accent/90 text-accent-foreground rounded-full p-4 shadow-lg hover:shadow-xl\n            transition-all duration-300 transform hover:scale-110 group-hover:rotate-12\n            ${isHovered ? 'shadow-accent/50' : ''}\n          `}>\n            <MessageCircle size={24} className=\"transition-transform duration-300\" />\n          </div>\n\n          {/* Notification Dot */}\n          <div className=\"absolute -top-1 -right-1 w-3 h-3 bg-destructive rounded-full animate-ping\"></div>\n          <div className=\"absolute -top-1 -right-1 w-3 h-3 bg-destructive rounded-full\"></div>\n        </div>\n\n        {/* Hover Tooltip */}\n        <div className={`\n          absolute right-16 top-1/2 -translate-y-1/2 bg-primary text-primary-foreground px-3 py-2 rounded-lg text-sm whitespace-nowrap\n          transition-all duration-300 pointer-events-none shadow-lg\n          ${isHovered ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-2'}\n        `}>\n          Chat with us on WhatsApp\n          <div className=\"absolute top-1/2 -translate-y-1/2 -right-1 w-2 h-2 bg-primary rotate-45\"></div>\n        </div>\n      </button>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,oDAAoD;IACpD,MAAM,iBAAiB,kBAAkB,2CAA2C;IACpF,MAAM,UAAU,mBAAmB;IAEnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,IAAI,OAAO,WAAW,GAAG,KAAK;gBAC5B,aAAa;YACf,OAAO;gBACL,aAAa;YACf;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gDAAgD;QAChD,IAAI,WAAW;YACb,MAAM,QAAQ,WAAW;gBACvB,eAAe;gBACf,+BAA+B;gBAC/B,WAAW,IAAM,eAAe,QAAQ;YAC1C,GAAG;YAEH,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;KAAU;IAEd,MAAM,sBAAsB;QAC1B,MAAM,cAAc,CAAC,cAAc,EAAE,eAAe,OAAO,CAAC,WAAW,IAAI,MAAM,EAAE,SAAS;QAC5F,OAAO,IAAI,CAAC,aAAa,UAAU;IACrC;IAEA,IAAI,CAAC,WAAW;QACd,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAW,CAAC;;;QAGf,EAAE,eAAe,CAAC,YAAY,wCAAwC,mCAAmC;MAC3G,CAAC;;oBAAE;kCAED,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBACC,SAAS;gBACT,cAAc;oBACZ,aAAa;oBACb,eAAe;gBACjB;gBACA,cAAc,IAAM,aAAa;gBACjC,WAAW,CAAC,kCAAkC,EAC5C,YAAY,8BAA8B,4BAC1C;gBACF,cAAW;;kCAGX,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC;gCAAI,WAAW,CAAC;;;YAGf,EAAE,YAAY,qBAAqB,GAAG;UACxC,CAAC;0CACC,cAAA,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,MAAM;oCAAI,WAAU;;;;;;;;;;;0CAIrC,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,8OAAC;wBAAI,WAAW,CAAC;;;UAGf,EAAE,YAAY,8BAA8B,0BAA0B;QACxE,CAAC;;4BAAE;0CAED,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKzB", "debugId": null}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/theme-provider.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\n\ntype Theme = 'light' | 'dark';\n\ninterface ThemeContextType {\n  theme: Theme;\n  toggleTheme: () => void;\n  setTheme: (theme: Theme) => void;\n}\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\nexport function useTheme() {\n  const context = useContext(ThemeContext);\n  if (context === undefined) {\n    // Return default values during SSR or when outside provider\n    return {\n      theme: 'light' as Theme,\n      toggleTheme: () => {},\n      setTheme: () => {}\n    };\n  }\n  return context;\n}\n\ninterface ThemeProviderProps {\n  children: React.ReactNode;\n  defaultTheme?: Theme;\n}\n\nexport function ThemeProvider({ children, defaultTheme = 'light' }: ThemeProviderProps) {\n  const [theme, setThemeState] = useState<Theme>(defaultTheme);\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    // Check for saved theme preference or default to 'light'\n    const savedTheme = localStorage.getItem('theme') as Theme;\n    if (savedTheme && (savedTheme === 'light' || savedTheme === 'dark')) {\n      setThemeState(savedTheme);\n    } else {\n      // Check system preference\n      const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n      setThemeState(systemPrefersDark ? 'dark' : 'light');\n    }\n    setMounted(true);\n  }, []);\n\n  useEffect(() => {\n    if (!mounted) return;\n\n    const root = window.document.documentElement;\n    root.classList.remove('light', 'dark');\n    root.classList.add(theme);\n    localStorage.setItem('theme', theme);\n  }, [theme, mounted]);\n\n  const toggleTheme = () => {\n    setThemeState(prevTheme => prevTheme === 'light' ? 'dark' : 'light');\n  };\n\n  const setTheme = (newTheme: Theme) => {\n    setThemeState(newTheme);\n  };\n\n  // Prevent hydration mismatch by not rendering until mounted\n  if (!mounted) {\n    return <div style={{ visibility: 'hidden' }}>{children}</div>;\n  }\n\n  return (\n    <ThemeContext.Provider value={{ theme, toggleTheme, setTheme }}>\n      {children}\n    </ThemeContext.Provider>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAYA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,4DAA4D;QAC5D,OAAO;YACL,OAAO;YACP,aAAa,KAAO;YACpB,UAAU,KAAO;QACnB;IACF;IACA,OAAO;AACT;AAOO,SAAS,cAAc,EAAE,QAAQ,EAAE,eAAe,OAAO,EAAsB;IACpF,MAAM,CAAC,OAAO,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,yDAAyD;QACzD,MAAM,aAAa,aAAa,OAAO,CAAC;QACxC,IAAI,cAAc,CAAC,eAAe,WAAW,eAAe,MAAM,GAAG;YACnE,cAAc;QAChB,OAAO;YACL,0BAA0B;YAC1B,MAAM,oBAAoB,OAAO,UAAU,CAAC,gCAAgC,OAAO;YACnF,cAAc,oBAAoB,SAAS;QAC7C;QACA,WAAW;IACb,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS;QAEd,MAAM,OAAO,OAAO,QAAQ,CAAC,eAAe;QAC5C,KAAK,SAAS,CAAC,MAAM,CAAC,SAAS;QAC/B,KAAK,SAAS,CAAC,GAAG,CAAC;QACnB,aAAa,OAAO,CAAC,SAAS;IAChC,GAAG;QAAC;QAAO;KAAQ;IAEnB,MAAM,cAAc;QAClB,cAAc,CAAA,YAAa,cAAc,UAAU,SAAS;IAC9D;IAEA,MAAM,WAAW,CAAC;QAChB,cAAc;IAChB;IAEA,4DAA4D;IAC5D,IAAI,CAAC,SAAS;QACZ,qBAAO,8OAAC;YAAI,OAAO;gBAAE,YAAY;YAAS;sBAAI;;;;;;IAChD;IAEA,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;YAAO;YAAa;QAAS;kBAC1D;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 318, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 375, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/theme-toggle.tsx"], "sourcesContent": ["'use client';\n\nimport { Moon, Sun } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { useTheme } from '@/components/theme-provider';\nimport { useEffect, useState } from 'react';\n\nexport function ThemeToggle() {\n  const { theme, toggleTheme } = useTheme();\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  if (!mounted) {\n    return (\n      <Button\n        variant=\"ghost\"\n        size=\"icon\"\n        className=\"relative h-9 w-9 text-foreground hover:text-accent hover:bg-foreground/10 transition-colors duration-300\"\n        aria-label=\"Toggle theme\"\n        disabled\n      >\n        <Sun className=\"h-4 w-4\" />\n      </Button>\n    );\n  }\n\n  return (\n    <Button\n      variant=\"ghost\"\n      size=\"icon\"\n      onClick={toggleTheme}\n      className=\"relative h-9 w-9 text-foreground hover:text-accent hover:bg-foreground/10 transition-colors duration-300\"\n      aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} theme`}\n    >\n      <Sun \n        className={`h-4 w-4 transition-all duration-300 ${\n          theme === 'dark' \n            ? 'rotate-90 scale-0 opacity-0' \n            : 'rotate-0 scale-100 opacity-100'\n        }`} \n      />\n      <Moon \n        className={`absolute h-4 w-4 transition-all duration-300 ${\n          theme === 'dark' \n            ? 'rotate-0 scale-100 opacity-100' \n            : '-rotate-90 scale-0 opacity-0'\n        }`} \n      />\n    </Button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AACA;AALA;;;;;;AAOO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,WAAQ,AAAD;IACtC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC,kIAAA,CAAA,SAAM;YACL,SAAQ;YACR,MAAK;YACL,WAAU;YACV,cAAW;YACX,QAAQ;sBAER,cAAA,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,SAAQ;QACR,MAAK;QACL,SAAS;QACT,WAAU;QACV,cAAY,CAAC,UAAU,EAAE,UAAU,UAAU,SAAS,QAAQ,MAAM,CAAC;;0BAErE,8OAAC,gMAAA,CAAA,MAAG;gBACF,WAAW,CAAC,oCAAoC,EAC9C,UAAU,SACN,gCACA,kCACJ;;;;;;0BAEJ,8OAAC,kMAAA,CAAA,OAAI;gBACH,WAAW,CAAC,6CAA6C,EACvD,UAAU,SACN,mCACA,gCACJ;;;;;;;;;;;;AAIV", "debugId": null}}, {"offset": {"line": 450, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { Menu, X, ChevronDown } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { ThemeToggle } from '@/components/theme-toggle';\n\nconst navigation = [\n  { name: 'Home', href: '#home' },\n  {\n    name: 'Videos',\n    href: '#featured-work',\n    dropdown: [\n      { name: 'Featured Videos', href: '#featured-work' },\n      { name: 'View All Videos', href: '/videos' }\n    ]\n  },\n  { name: 'Clients', href: '#clients' },\n  {\n    name: '<PERSON><PERSON>',\n    href: '#reels',\n    dropdown: [\n      { name: 'Featured Reels', href: '#reels' },\n      { name: 'View All Reels', href: '/reels' }\n    ]\n  },\n  {\n    name: 'Blog',\n    href: '#blog',\n    dropdown: [\n      { name: 'Latest Posts', href: '#blog' },\n      { name: 'View All Posts', href: '/blog' }\n    ]\n  },\n  { name: 'Testimonials', href: '#testimonials' },\n  { name: 'Contact', href: '#contact' },\n];\n\nexport default function Header() {\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [activeSection, setActiveSection] = useState('home');\n  const [openDropdown, setOpenDropdown] = useState<string | null>(null);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n\n      // Update active section based on scroll position\n      const sections = navigation.map(item => item.href.substring(1));\n      const currentSection = sections.find(section => {\n        const element = document.getElementById(section);\n        if (element) {\n          const rect = element.getBoundingClientRect();\n          return rect.top <= 100 && rect.bottom >= 100;\n        }\n        return false;\n      });\n\n      if (currentSection) {\n        setActiveSection(currentSection);\n      }\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const handleNavClick = (href: string) => {\n    if (href.startsWith('#')) {\n      // Handle anchor links\n      const targetId = href.substring(1);\n      const element = document.getElementById(targetId);\n      if (element) {\n        const headerOffset = 80;\n        const elementPosition = element.getBoundingClientRect().top;\n        const offsetPosition = elementPosition + window.pageYOffset - headerOffset;\n\n        window.scrollTo({\n          top: offsetPosition,\n          behavior: 'smooth'\n        });\n      }\n    } else {\n      // Handle regular page navigation\n      window.location.href = href;\n    }\n    setIsMobileMenuOpen(false);\n    setOpenDropdown(null);\n  };\n\n  return (\n    <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${isScrolled\n      ? 'bg-background/95 backdrop-blur-md shadow-lg border-b border-foreground/10'\n      : 'bg-background/90 backdrop-blur-sm'\n      }`}>\n      <nav className=\"container mx-auto px-4 py-3\">\n        <div className=\"flex items-center justify-between\">\n          {/* Logo */}\n          <Link\n            href=\"#home\"\n            className=\"text-2xl font-heading font-bold text-foreground hover:text-accent transition-colors duration-300 hover:scale-105 transform\"\n            onClick={(e) => {\n              e.preventDefault();\n              handleNavClick('#home');\n            }}\n          >\n            UR\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-6\">\n            {navigation.map((item) => (\n              <div key={item.name} className=\"relative group\">\n                {item.dropdown ? (\n                  <>\n                    <button\n                      onClick={() => handleNavClick(item.href)}\n                      onMouseEnter={() => setOpenDropdown(item.name)}\n                      onMouseLeave={() => setOpenDropdown(null)}\n                      className={`relative text-sm font-semibold transition-colors duration-300 py-2 px-1 flex items-center gap-1 ${activeSection === item.href.substring(1)\n                        ? 'text-accent'\n                        : 'text-foreground hover:text-accent'\n                        }`}\n                    >\n                      {item.name}\n                      <ChevronDown size={14} className={`transition-transform duration-200 ${openDropdown === item.name ? 'rotate-180' : ''\n                        }`} />\n                      <span className={`absolute bottom-0 left-0 h-0.5 bg-accent transition-all duration-300 ${activeSection === item.href.substring(1) ? 'w-full' : 'w-0 group-hover:w-full'\n                        }`} />\n                    </button>\n\n                    {/* Dropdown Menu */}\n                    <div\n                      className={`absolute top-full left-0 mt-2 w-48 bg-background/95 backdrop-blur-md border border-foreground/10 rounded-lg shadow-lg transition-all duration-200 ${openDropdown === item.name ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible -translate-y-2'\n                        }`}\n                      onMouseEnter={() => setOpenDropdown(item.name)}\n                      onMouseLeave={() => setOpenDropdown(null)}\n                    >\n                      {item.dropdown.map((dropdownItem) => (\n                        <button\n                          key={dropdownItem.name}\n                          onClick={() => handleNavClick(dropdownItem.href)}\n                          className=\"block w-full text-left px-4 py-3 text-sm text-foreground hover:text-accent hover:bg-foreground/10 transition-colors duration-200 first:rounded-t-lg last:rounded-b-lg\"\n                        >\n                          {dropdownItem.name}\n                        </button>\n                      ))}\n                    </div>\n                  </>\n                ) : (\n                  <button\n                    onClick={() => handleNavClick(item.href)}\n                    className={`relative text-sm font-semibold transition-colors duration-300 py-2 px-1 ${activeSection === item.href.substring(1)\n                      ? 'text-accent'\n                      : 'text-foreground hover:text-accent'\n                      }`}\n                  >\n                    {item.name}\n                    <span className={`absolute bottom-0 left-0 h-0.5 bg-accent transition-all duration-300 ${activeSection === item.href.substring(1) ? 'w-full' : 'w-0 group-hover:w-full'\n                      }`} />\n                  </button>\n                )}\n              </div>\n            ))}\n\n            {/* Theme Toggle */}\n            <ThemeToggle />\n          </div>\n\n          {/* Mobile Navigation Controls */}\n          <div className=\"flex items-center space-x-2 md:hidden\">\n            <ThemeToggle />\n\n            {/* Mobile Menu Button */}\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              className=\"text-foreground hover:text-accent hover:bg-foreground/10\"\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n              aria-label=\"Toggle menu\"\n            >\n              {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}\n            </Button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        <div className={`md:hidden transition-all duration-300 overflow-hidden ${isMobileMenuOpen ? 'max-h-[500px] opacity-100' : 'max-h-0 opacity-0'\n          }`}>\n          <div className=\"py-4 space-y-2 border-t border-foreground/10 mt-4\">\n            {navigation.map((item) => (\n              <div key={item.name}>\n                <button\n                  onClick={() => handleNavClick(item.href)}\n                  className={`block w-full text-left px-4 py-3 text-sm font-semibold transition-colors duration-300 rounded-lg ${activeSection === item.href.substring(1)\n                    ? 'text-accent bg-foreground/10'\n                    : 'text-foreground hover:text-accent hover:bg-foreground/5'\n                    }`}\n                >\n                  {item.name}\n                </button>\n\n                {/* Mobile Dropdown Items */}\n                {item.dropdown && (\n                  <div className=\"ml-4 mt-1 space-y-1\">\n                    {item.dropdown.map((dropdownItem) => (\n                      <button\n                        key={dropdownItem.name}\n                        onClick={() => handleNavClick(dropdownItem.href)}\n                        className=\"block w-full text-left px-4 py-2 text-xs text-foreground/80 hover:text-accent hover:bg-foreground/5 transition-colors duration-300 rounded-lg\"\n                      >\n                        {dropdownItem.name}\n                      </button>\n                    ))}\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n        </div>\n      </nav>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AANA;;;;;;;AAQA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAQ;IAC9B;QACE,MAAM;QACN,MAAM;QACN,UAAU;YACR;gBAAE,MAAM;gBAAmB,MAAM;YAAiB;YAClD;gBAAE,MAAM;gBAAmB,MAAM;YAAU;SAC5C;IACH;IACA;QAAE,MAAM;QAAW,MAAM;IAAW;IACpC;QACE,MAAM;QACN,MAAM;QACN,UAAU;YACR;gBAAE,MAAM;gBAAkB,MAAM;YAAS;YACzC;gBAAE,MAAM;gBAAkB,MAAM;YAAS;SAC1C;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,UAAU;YACR;gBAAE,MAAM;gBAAgB,MAAM;YAAQ;YACtC;gBAAE,MAAM;gBAAkB,MAAM;YAAQ;SACzC;IACH;IACA;QAAE,MAAM;QAAgB,MAAM;IAAgB;IAC9C;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAEc,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,cAAc,OAAO,OAAO,GAAG;YAE/B,iDAAiD;YACjD,MAAM,WAAW,WAAW,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,SAAS,CAAC;YAC5D,MAAM,iBAAiB,SAAS,IAAI,CAAC,CAAA;gBACnC,MAAM,UAAU,SAAS,cAAc,CAAC;gBACxC,IAAI,SAAS;oBACX,MAAM,OAAO,QAAQ,qBAAqB;oBAC1C,OAAO,KAAK,GAAG,IAAI,OAAO,KAAK,MAAM,IAAI;gBAC3C;gBACA,OAAO;YACT;YAEA,IAAI,gBAAgB;gBAClB,iBAAiB;YACnB;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAC;QACtB,IAAI,KAAK,UAAU,CAAC,MAAM;YACxB,sBAAsB;YACtB,MAAM,WAAW,KAAK,SAAS,CAAC;YAChC,MAAM,UAAU,SAAS,cAAc,CAAC;YACxC,IAAI,SAAS;gBACX,MAAM,eAAe;gBACrB,MAAM,kBAAkB,QAAQ,qBAAqB,GAAG,GAAG;gBAC3D,MAAM,iBAAiB,kBAAkB,OAAO,WAAW,GAAG;gBAE9D,OAAO,QAAQ,CAAC;oBACd,KAAK;oBACL,UAAU;gBACZ;YACF;QACF,OAAO;YACL,iCAAiC;YACjC,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;QACA,oBAAoB;QACpB,gBAAgB;IAClB;IAEA,qBACE,8OAAC;QAAO,WAAW,CAAC,4DAA4D,EAAE,aAC9E,8EACA,qCACA;kBACF,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;4BACV,SAAS,CAAC;gCACR,EAAE,cAAc;gCAChB,eAAe;4BACjB;sCACD;;;;;;sCAKD,8OAAC;4BAAI,WAAU;;gCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;wCAAoB,WAAU;kDAC5B,KAAK,QAAQ,iBACZ;;8DACE,8OAAC;oDACC,SAAS,IAAM,eAAe,KAAK,IAAI;oDACvC,cAAc,IAAM,gBAAgB,KAAK,IAAI;oDAC7C,cAAc,IAAM,gBAAgB;oDACpC,WAAW,CAAC,gGAAgG,EAAE,kBAAkB,KAAK,IAAI,CAAC,SAAS,CAAC,KAChJ,gBACA,qCACA;;wDAEH,KAAK,IAAI;sEACV,8OAAC,oNAAA,CAAA,cAAW;4DAAC,MAAM;4DAAI,WAAW,CAAC,kCAAkC,EAAE,iBAAiB,KAAK,IAAI,GAAG,eAAe,IAC/G;;;;;;sEACJ,8OAAC;4DAAK,WAAW,CAAC,qEAAqE,EAAE,kBAAkB,KAAK,IAAI,CAAC,SAAS,CAAC,KAAK,WAAW,0BAC3I;;;;;;;;;;;;8DAIN,8OAAC;oDACC,WAAW,CAAC,kJAAkJ,EAAE,iBAAiB,KAAK,IAAI,GAAG,sCAAsC,sCAC/N;oDACJ,cAAc,IAAM,gBAAgB,KAAK,IAAI;oDAC7C,cAAc,IAAM,gBAAgB;8DAEnC,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,6BAClB,8OAAC;4DAEC,SAAS,IAAM,eAAe,aAAa,IAAI;4DAC/C,WAAU;sEAET,aAAa,IAAI;2DAJb,aAAa,IAAI;;;;;;;;;;;yEAU9B,8OAAC;4CACC,SAAS,IAAM,eAAe,KAAK,IAAI;4CACvC,WAAW,CAAC,wEAAwE,EAAE,kBAAkB,KAAK,IAAI,CAAC,SAAS,CAAC,KACxH,gBACA,qCACA;;gDAEH,KAAK,IAAI;8DACV,8OAAC;oDAAK,WAAW,CAAC,qEAAqE,EAAE,kBAAkB,KAAK,IAAI,CAAC,SAAS,CAAC,KAAK,WAAW,0BAC3I;;;;;;;;;;;;uCA/CA,KAAK,IAAI;;;;;8CAsDrB,8OAAC,qIAAA,CAAA,cAAW;;;;;;;;;;;sCAId,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,qIAAA,CAAA,cAAW;;;;;8CAGZ,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,oBAAoB,CAAC;oCACpC,cAAW;8CAEV,iCAAmB,8OAAC,4LAAA,CAAA,IAAC;wCAAC,MAAM;;;;;6DAAS,8OAAC,kMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;8BAMxD,8OAAC;oBAAI,WAAW,CAAC,sDAAsD,EAAE,mBAAmB,8BAA8B,qBACtH;8BACF,cAAA,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;;kDACC,8OAAC;wCACC,SAAS,IAAM,eAAe,KAAK,IAAI;wCACvC,WAAW,CAAC,iGAAiG,EAAE,kBAAkB,KAAK,IAAI,CAAC,SAAS,CAAC,KACjJ,iCACA,2DACA;kDAEH,KAAK,IAAI;;;;;;oCAIX,KAAK,QAAQ,kBACZ,8OAAC;wCAAI,WAAU;kDACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,6BAClB,8OAAC;gDAEC,SAAS,IAAM,eAAe,aAAa,IAAI;gDAC/C,WAAU;0DAET,aAAa,IAAI;+CAJb,aAAa,IAAI;;;;;;;;;;;+BAhBtB,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCjC", "debugId": null}}]}