@import url("https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Inter:wght@300;400;500;600;700&family=Space+Grotesk:wght@300;400;500;600;700&display=swap");
@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --font-heading: "Playfair Display", serif;
  --font-body: "Inter", sans-serif;
  --font-accent: "Space Grotesk", sans-serif;
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  /* Custom animations */
  --animate-float: float 6s ease-in-out infinite alternate;
  --animate-bounce-slow: bounce 2s infinite;
}

:root {
  --radius: 0.5rem;
  /* Custom Color Palette - Light Theme */
  --background: #F5F5F5;
  --foreground: #121212;
  --card: #FFFFFF;
  --card-foreground: #121212;
  --popover: #FFFFFF;
  --popover-foreground: #121212;
  --primary: #121212;
  --primary-foreground: #F5F5F5;
  --secondary: #E8E8E8;
  --secondary-foreground: #121212;
  --muted: #D1D1D1;
  --muted-foreground: #666666;
  --accent: #c6a664;
  --accent-foreground: #121212;
  --destructive: #dc2626;
  --destructive-foreground: #F5F5F5;
  --border: #D4D4D4;
  --input: #FFFFFF;
  --ring: #c6a664;
  --chart-1: #c6a664;
  --chart-2: #8b5cf6;
  --chart-3: #06b6d4;
  --chart-4: #10b981;
  --chart-5: #f59e0b;
  --sidebar: #FFFFFF;
  --sidebar-foreground: #121212;
  --sidebar-primary: #121212;
  --sidebar-primary-foreground: #F5F5F5;
  --sidebar-accent: #E8E8E8;
  --sidebar-accent-foreground: #121212;
  --sidebar-border: #D4D4D4;
  --sidebar-ring: #c6a664;
}

.dark {
  /* Custom Color Palette - Dark Theme */
  --background: #121212;
  --foreground: #F5F5F5;
  --card: #1E1E1E;
  --card-foreground: #F5F5F5;
  --popover: #1E1E1E;
  --popover-foreground: #F5F5F5;
  --primary: #F5F5F5;
  --primary-foreground: #121212;
  --secondary: #2A2A2A;
  --secondary-foreground: #F5F5F5;
  --muted: #404040;
  --muted-foreground: #B0B0B0;
  --accent: #c6a664;
  --accent-foreground: #121212;
  --destructive: #ef4444;
  --destructive-foreground: #F5F5F5;
  --border: #333333;
  --input: #1E1E1E;
  --ring: #c6a664;
  --chart-1: #c6a664;
  --chart-2: #8b5cf6;
  --chart-3: #06b6d4;
  --chart-4: #10b981;
  --chart-5: #f59e0b;
  --sidebar: #1E1E1E;
  --sidebar-foreground: #F5F5F5;
  --sidebar-primary: #F5F5F5;
  --sidebar-primary-foreground: #121212;
  --sidebar-accent: #2A2A2A;
  --sidebar-accent-foreground: #F5F5F5;
  --sidebar-border: #333333;
  --sidebar-ring: #c6a664;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground font-body;
  }
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-heading text-primary;
  }
}

@layer utilities {
  .animate-float {
    animation: float 6s ease-in-out infinite alternate;
  }

  .animate-bounce-slow {
    animation: bounce 2s infinite;
  }
}

@keyframes float {
  from {
    transform: translateY(-10px);
  }
  to {
    transform: translateY(10px);
  }
}

/* WhatsApp Button Animation Delay */
.animation-delay-1000 {
  animation-delay: 1s;
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Fade in up animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
  opacity: 0;
}

@keyframes heartbeat {
  0%,
  100% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.3);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.3);
  }
  70% {
    transform: scale(1);
  }
}

@keyframes blink {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}

.heartbeat {
  animation: heartbeat 1.5s infinite;
}

.blink {
  animation: blink 1.5s infinite;
}
