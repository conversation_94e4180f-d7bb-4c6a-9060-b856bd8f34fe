'use client';

import { useState } from 'react';
import { Building } from 'lucide-react';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import ClientCard from '@/components/ClientCard';
import { type Client } from '@/lib/api';

interface ClientsGridProps {
  clients: Client[];
  allClients: Client[];
  viewMode: 'grid' | 'list';
  industryFilter: string;
}

export default function ClientsGrid({ 
  clients, 
  allClients, 
  viewMode, 
  industryFilter 
}: ClientsGridProps) {
  const [selectedClient, setSelectedClient] = useState<string | null>(null);

  const openClientModal = (clientId: string) => {
    setSelectedClient(clientId);
  };

  const selectedClientData = allClients.find(client => client._id === selectedClient);

  if (clients.length === 0) {
    return (
      <div className="text-center py-16">
        <div className="bg-muted rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6">
          <Building size={48} className="text-muted-foreground" />
        </div>
        <h3 className="text-xl font-semibold text-gray-600 mb-2">
          No clients found
        </h3>
        <p className="text-muted-foreground">
          {industryFilter === 'all' 
            ? 'No clients available at the moment.' 
            : `No clients found in the ${industryFilter} industry.`
          }
        </p>
      </div>
    );
  }

  const gridClasses = viewMode === 'grid' 
    ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" 
    : "space-y-6";

  return (
    <div className={gridClasses}>
      {clients.map((client, index) => (
        <Dialog key={client._id}>
          <DialogTrigger asChild>
            <div 
              className="group"
              style={{
                animationDelay: `${index * 100}ms`,
              }}
            >
              <ClientCard
                client={client}
                variant={viewMode === 'grid' ? 'compact' : 'compact'}
                onClick={() => openClientModal(client._id)}
                showWebsite={viewMode === 'list'}
                className={`
                  hover:shadow-xl transition-all duration-500 hover:-translate-y-1
                  ${viewMode === 'list' ? 'flex items-center gap-6 p-6' : ''}
                  animate-fade-in-up
                `}
              />
            </div>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            {selectedClient === client._id && selectedClientData && (
              <ClientCard
                client={selectedClientData}
                variant="detailed"
                showWebsite={true}
              />
            )}
          </DialogContent>
        </Dialog>
      ))}
    </div>
  );
}
