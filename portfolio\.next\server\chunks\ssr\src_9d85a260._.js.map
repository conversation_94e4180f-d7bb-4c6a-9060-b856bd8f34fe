{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport Image from 'next/image';\nimport Link from 'next/link';\nimport { ChevronDown, Linkedin, Instagram, Youtube, Facebook } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\n\nexport default function Hero() {\n  const typedTextRef = useRef<HTMLSpanElement>(null);\n\n  useEffect(() => {\n    // Simple typing animation\n    const texts = ['Video Editor', 'Storyteller', 'Motion Graphics Artist', 'Visual Craftsman'];\n    let textIndex = 0;\n    let charIndex = 0;\n    let isDeleting = false;\n    let currentText = '';\n\n    const typeText = () => {\n      const fullText = texts[textIndex];\n\n      if (isDeleting) {\n        currentText = fullText.substring(0, charIndex - 1);\n        charIndex--;\n      } else {\n        currentText = fullText.substring(0, charIndex + 1);\n        charIndex++;\n      }\n\n      if (typedTextRef.current) {\n        typedTextRef.current.textContent = currentText;\n      }\n\n      let typeSpeed = isDeleting ? 40 : 60;\n\n      if (!isDeleting && charIndex === fullText.length) {\n        typeSpeed = 1500; // Pause at end\n        isDeleting = true;\n      } else if (isDeleting && charIndex === 0) {\n        isDeleting = false;\n        textIndex = (textIndex + 1) % texts.length;\n        typeSpeed = 500; // Pause before next word\n      }\n\n      setTimeout(typeText, typeSpeed);\n    };\n\n    typeText();\n  }, []);\n\n  const handleScrollToWork = () => {\n    const element = document.getElementById('featured-work');\n    if (element) {\n      const headerOffset = 80;\n      const elementPosition = element.getBoundingClientRect().top;\n      const offsetPosition = elementPosition + window.pageYOffset - headerOffset;\n\n      window.scrollTo({\n        top: offsetPosition,\n        behavior: 'smooth'\n      });\n    }\n  };\n\n  const handleScrollToContact = () => {\n    const element = document.getElementById('contact');\n    if (element) {\n      const headerOffset = 80;\n      const elementPosition = element.getBoundingClientRect().top;\n      const offsetPosition = elementPosition + window.pageYOffset - headerOffset;\n\n      window.scrollTo({\n        top: offsetPosition,\n        behavior: 'smooth'\n      });\n    }\n  };\n\n  return (\n    <section\n      id=\"home\"\n      className=\"relative min-h-screen flex items-center bg-background overflow-hidden\"\n    >\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute top-0 right-0 w-1/2 h-full bg-gradient-to-l from-accent/20 to-transparent transform skew-x-12\"></div>\n      </div>\n\n      <div className=\"container mx-auto px-4 py-20 relative z-10\">\n        <div className=\"flex flex-col lg:flex-row items-center justify-between gap-12\">\n          {/* Text Content */}\n          <div className=\"flex-1 max-w-2xl text-center lg:text-left\">\n            <h1 className=\"text-4xl md:text-5xl lg:text-6xl font-heading font-bold text-foreground mb-4 animate-in slide-in-from-left duration-1000\">\n              Uttam Rimal\n            </h1>\n            <h2 className=\"text-xl md:text-2xl lg:text-3xl font-body font-semibold text-accent mb-6 animate-in slide-in-from-left duration-1000 delay-200\">\n              Creative Video Editor & Storyteller\n            </h2>\n            <p className=\"text-lg text-muted-foreground mb-8 leading-relaxed animate-in slide-in-from-left duration-1000 delay-400\">\n              Transforming footage into compelling narratives. I'm a{' '}\n              <span ref={typedTextRef} className=\"text-accent font-semibold\"></span>\n              <span className=\"animate-pulse\">_</span>\n              {' '}with over a year of experience crafting visually stunning videos that engage and inspire.\n            </p>\n\n            {/* CTA Buttons */}\n            <div className=\"flex flex-col sm:flex-row gap-4 mb-8 animate-in slide-in-from-left duration-1000 delay-600\">\n              <Button\n                onClick={handleScrollToWork}\n                className=\"bg-accent hover:bg-accent/90 text-background px-8 py-3 rounded-full font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg\"\n              >\n                View My Work\n              </Button>\n              <Button\n                variant=\"outline\"\n                onClick={handleScrollToContact}\n                className=\"border-foreground text-foreground hover:bg-foreground hover:text-background px-8 py-3 rounded-full font-semibold transition-all duration-300 hover:scale-105\"\n              >\n                Get a Quote\n              </Button>\n            </div>\n\n            {/* Social Links */}\n            <div className=\"flex justify-center lg:justify-start gap-4 animate-in slide-in-from-left duration-1000 delay-800\">\n              {[\n                { icon: Linkedin, href: 'https://www.linkedin.com/in/uttamrimal', label: 'LinkedIn' },\n                { icon: Instagram, href: 'https://www.instagram.com/uttamrimal', label: 'Instagram' },\n                { icon: Youtube, href: 'https://www.youtube.com/c/UttamRimal', label: 'YouTube' },\n                { icon: Facebook, href: 'https://www.facebook.com/uttamrimal', label: 'Facebook' },\n              ].map(({ icon: Icon, href, label }) => (\n                <Link\n                  key={label}\n                  href={href}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"w-12 h-12 bg-foreground/10 hover:bg-accent text-foreground hover:text-background rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 hover:shadow-lg\"\n                  aria-label={label}\n                >\n                  <Icon size={20} />\n                </Link>\n              ))}\n            </div>\n          </div>\n\n          {/* Profile Image */}\n          <div className=\"flex-1 max-w-md animate-in slide-in-from-right duration-1000 delay-300\">\n            <div className=\"relative\">\n              <div className=\"w-80 h-80 mx-auto relative\">\n                <Image\n                  src=\"/images/uttam_rimal.jpg\"\n                  alt=\"Uttam Rimal - Professional Video Editor\"\n                  fill\n                  className=\"rounded-full object-cover border-4 border-foreground/20 shadow-2xl animate-float\"\n                  priority\n                />\n              </div>\n              {/* Decorative elements */}\n              <div className=\"absolute -top-4 -right-4 w-24 h-24 bg-accent/20 rounded-full blur-xl\"></div>\n              <div className=\"absolute -bottom-4 -left-4 w-32 h-32 bg-accent/30 rounded-full blur-xl\"></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Scroll Down Arrow */}\n      <button\n        onClick={handleScrollToWork}\n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 text-foreground/80 hover:text-foreground transition-colors duration-300 animate-bounce-slow\"\n        aria-label=\"Scroll down\"\n      >\n        <ChevronDown size={32} />\n      </button>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAmB;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,0BAA0B;QAC1B,MAAM,QAAQ;YAAC;YAAgB;YAAe;YAA0B;SAAmB;QAC3F,IAAI,YAAY;QAChB,IAAI,YAAY;QAChB,IAAI,aAAa;QACjB,IAAI,cAAc;QAElB,MAAM,WAAW;YACf,MAAM,WAAW,KAAK,CAAC,UAAU;YAEjC,IAAI,YAAY;gBACd,cAAc,SAAS,SAAS,CAAC,GAAG,YAAY;gBAChD;YACF,OAAO;gBACL,cAAc,SAAS,SAAS,CAAC,GAAG,YAAY;gBAChD;YACF;YAEA,IAAI,aAAa,OAAO,EAAE;gBACxB,aAAa,OAAO,CAAC,WAAW,GAAG;YACrC;YAEA,IAAI,YAAY,aAAa,KAAK;YAElC,IAAI,CAAC,cAAc,cAAc,SAAS,MAAM,EAAE;gBAChD,YAAY,MAAM,eAAe;gBACjC,aAAa;YACf,OAAO,IAAI,cAAc,cAAc,GAAG;gBACxC,aAAa;gBACb,YAAY,CAAC,YAAY,CAAC,IAAI,MAAM,MAAM;gBAC1C,YAAY,KAAK,yBAAyB;YAC5C;YAEA,WAAW,UAAU;QACvB;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,IAAI,SAAS;YACX,MAAM,eAAe;YACrB,MAAM,kBAAkB,QAAQ,qBAAqB,GAAG,GAAG;YAC3D,MAAM,iBAAiB,kBAAkB,OAAO,WAAW,GAAG;YAE9D,OAAO,QAAQ,CAAC;gBACd,KAAK;gBACL,UAAU;YACZ;QACF;IACF;IAEA,MAAM,wBAAwB;QAC5B,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,IAAI,SAAS;YACX,MAAM,eAAe;YACrB,MAAM,kBAAkB,QAAQ,qBAAqB,GAAG,GAAG;YAC3D,MAAM,iBAAiB,kBAAkB,OAAO,WAAW,GAAG;YAE9D,OAAO,QAAQ,CAAC;gBACd,KAAK;gBACL,UAAU;YACZ;QACF;IACF;IAEA,qBACE,8OAAC;QACC,IAAG;QACH,WAAU;;0BAGV,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2H;;;;;;8CAGzI,8OAAC;oCAAG,WAAU;8CAAiI;;;;;;8CAG/I,8OAAC;oCAAE,WAAU;;wCAA2G;wCAC/D;sDACvD,8OAAC;4CAAK,KAAK;4CAAc,WAAU;;;;;;sDACnC,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;wCAC/B;wCAAI;;;;;;;8CAIP,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,WAAU;sDACX;;;;;;sDAGD,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;;8CAMH,8OAAC;oCAAI,WAAU;8CACZ;wCACC;4CAAE,MAAM,0MAAA,CAAA,WAAQ;4CAAE,MAAM;4CAA0C,OAAO;wCAAW;wCACpF;4CAAE,MAAM,4MAAA,CAAA,YAAS;4CAAE,MAAM;4CAAwC,OAAO;wCAAY;wCACpF;4CAAE,MAAM,wMAAA,CAAA,UAAO;4CAAE,MAAM;4CAAwC,OAAO;wCAAU;wCAChF;4CAAE,MAAM,0MAAA,CAAA,WAAQ;4CAAE,MAAM;4CAAuC,OAAO;wCAAW;qCAClF,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,iBAChC,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM;4CACN,QAAO;4CACP,KAAI;4CACJ,WAAU;4CACV,cAAY;sDAEZ,cAAA,8OAAC;gDAAK,MAAM;;;;;;2CAPP;;;;;;;;;;;;;;;;sCAcb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,IAAI;4CACJ,WAAU;4CACV,QAAQ;;;;;;;;;;;kDAIZ,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvB,8OAAC;gBACC,SAAS;gBACT,WAAU;gBACV,cAAW;0BAEX,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,MAAM;;;;;;;;;;;;;;;;;AAI3B", "debugId": null}}, {"offset": {"line": 329, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\">\n          <XIcon />\n          <span className=\"sr-only\">Close</span>\n        </DialogPrimitive.Close>\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,GAAG,OACkD;IACrD,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 502, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 548, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/lib/api.ts"], "sourcesContent": ["// API integration for fetching content from CMS\nconst CMS_BASE_URL = process.env.NEXT_PUBLIC_CMS_URL || \"http://localhost:3002\";\n\n// Types for API responses\nexport interface BlogPost {\n  _id: string;\n  title: string;\n  slug: string;\n  excerpt: string;\n  content: string;\n  thumbnail: string;\n  category: string;\n  tags: string[];\n  featured: boolean;\n  status: \"draft\" | \"published\" | \"archived\";\n  readTime: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Video {\n  _id: string;\n  id: string;\n  title: string;\n  slug: string;\n  description?: string;\n  category?: string;\n  tags: string[];\n  featured: boolean;\n  status: \"draft\" | \"published\" | \"archived\";\n  order: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Reel {\n  _id: string;\n  id: string;\n  title: string;\n  slug: string;\n  thumbnail?: string; // Generated dynamically from YouTube API\n  description?: string;\n  platform: \"youtube\";\n  embedUrl?: string;\n  featured: boolean;\n  status: \"draft\" | \"published\" | \"archived\";\n  order: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Testimonial {\n  _id: string;\n  name: string;\n  slug: string;\n  avatar?: string;\n  role: string;\n  company?: string;\n  email?: string;\n  linkedinUrl?: string;\n  content: string;\n  rating: number;\n  featured: boolean;\n  status: \"draft\" | \"published\" | \"archived\";\n  order: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Client {\n  _id: string;\n  name: string;\n  slug: string;\n  logo: string;\n  description: string;\n  website?: string;\n  industry?: string;\n  projectType?: string;\n  featured: boolean;\n  status: \"draft\" | \"published\" | \"archived\";\n  order: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// API response wrapper\ninterface ApiResponse<T> {\n  success: boolean;\n  data: T;\n  message?: string;\n}\n\n// Generic fetch function with error handling\nasync function fetchFromCMS<T>(endpoint: string): Promise<T[]> {\n  try {\n    const response = await fetch(`${CMS_BASE_URL}/api${endpoint}`, {\n      method: \"GET\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      // Add cache control for better performance\n      next: { revalidate: 300 }, // Revalidate every 5 minutes\n    });\n\n    if (!response.ok) {\n      console.error(\n        `Failed to fetch ${endpoint}:`,\n        response.status,\n        response.statusText\n      );\n      return [];\n    }\n\n    const result: ApiResponse<T[]> = await response.json();\n\n    if (!result.success) {\n      console.error(`API error for ${endpoint}:`, result.message);\n      return [];\n    }\n\n    return result.data || [];\n  } catch (error) {\n    console.error(`Network error fetching ${endpoint}:`, error);\n    return [];\n  }\n}\n\n// Blog API functions\nexport async function getBlogPosts(): Promise<BlogPost[]> {\n  const posts = await fetchFromCMS<BlogPost>(\"/blog\");\n  // Only return published posts, sorted by creation date\n  return posts\n    .filter((post) => post.status === \"published\")\n    .sort(\n      (a, b) =>\n        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()\n    );\n}\n\nexport async function getFeaturedBlogPosts(): Promise<BlogPost[]> {\n  const posts = await getBlogPosts();\n  return posts.filter((post) => post.featured);\n}\n\nexport async function getBlogPostBySlug(\n  slug: string\n): Promise<BlogPost | null> {\n  try {\n    const response = await fetch(`${CMS_BASE_URL}/api/blog/slug/${slug}`, {\n      method: \"GET\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      next: { revalidate: 300 },\n    });\n\n    if (!response.ok) {\n      return null;\n    }\n\n    const result: ApiResponse<BlogPost> = await response.json();\n    return result.success ? result.data : null;\n  } catch (error) {\n    console.error(\"Error fetching blog post by slug:\", error);\n    return null;\n  }\n}\n\n// Videos API functions\nexport async function getVideos(): Promise<Video[]> {\n  const videos = await fetchFromCMS<Video>(\"/videos\");\n  return videos\n    .filter((video) => video.status === \"published\")\n    .sort((a, b) => a.order - b.order);\n}\n\nexport async function getFeaturedVideos(): Promise<Video[]> {\n  const videos = await getVideos();\n  return videos.filter((video) => video.featured);\n}\n\n// Reels API functions\nexport async function getReels(): Promise<Reel[]> {\n  const reels = await fetchFromCMS<Reel>(\"/reels\");\n  return reels\n    .filter((reel) => reel.status === \"published\")\n    .sort((a, b) => a.order - b.order);\n}\n\nexport async function getFeaturedReels(): Promise<Reel[]> {\n  const reels = await getReels();\n  return reels.filter((reel) => reel.featured);\n}\n\n// Testimonials API functions\nexport async function getTestimonials(): Promise<Testimonial[]> {\n  const testimonials = await fetchFromCMS<Testimonial>(\"/testimonials\");\n  return testimonials\n    .filter((testimonial) => testimonial.status === \"published\")\n    .sort((a, b) => a.order - b.order);\n}\n\nexport async function getFeaturedTestimonials(): Promise<Testimonial[]> {\n  const testimonials = await getTestimonials();\n  return testimonials.filter((testimonial) => testimonial.featured);\n}\n\n// Clients API functions\nexport async function getClients(): Promise<Client[]> {\n  const clients = await fetchFromCMS<Client>(\"/clients\");\n  return clients\n    .filter((client) => client.status === \"published\")\n    .sort((a, b) => a.order - b.order);\n}\n\nexport async function getFeaturedClients(): Promise<Client[]> {\n  const clients = await getClients();\n  return clients.filter((client) => client.featured);\n}\n\n// Utility functions\nexport function getYouTubeThumbnail(videoId: string): string {\n  return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n}\n\nexport function generateEmbedUrl(id: string): string {\n  // Only YouTube Shorts are supported now\n  return `https://www.youtube.com/embed/${id}`;\n}\n\nexport function getYouTubeShortsThumbnail(videoId: string): string {\n  return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n}\n\n// Helper function to ensure reels have thumbnails\nexport function ensureReelThumbnail(reel: Reel): Reel {\n  return {\n    ...reel,\n    thumbnail: reel.thumbnail || getYouTubeShortsThumbnail(reel.id),\n  };\n}\n\nexport function formatDate(dateString: string): string {\n  return new Date(dateString).toLocaleDateString(\"en-US\", {\n    year: \"numeric\",\n    month: \"long\",\n    day: \"numeric\",\n  });\n}\n\nexport function calculateReadTime(content: string): number {\n  const wordsPerMinute = 200;\n  const wordCount = content.split(/\\s+/).length;\n  return Math.ceil(wordCount / wordsPerMinute);\n}\n"], "names": [], "mappings": "AAAA,gDAAgD;;;;;;;;;;;;;;;;;;;;AAChD,MAAM,eAAe,wEAAmC;AA2FxD,6CAA6C;AAC7C,eAAe,aAAgB,QAAgB;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,IAAI,EAAE,UAAU,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,2CAA2C;YAC3C,MAAM;gBAAE,YAAY;YAAI;QAC1B;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CACX,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC,EAC9B,SAAS,MAAM,EACf,SAAS,UAAU;YAErB,OAAO,EAAE;QACX;QAEA,MAAM,SAA2B,MAAM,SAAS,IAAI;QAEpD,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC,EAAE,OAAO,OAAO;YAC1D,OAAO,EAAE;QACX;QAEA,OAAO,OAAO,IAAI,IAAI,EAAE;IAC1B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,SAAS,CAAC,CAAC,EAAE;QACrD,OAAO,EAAE;IACX;AACF;AAGO,eAAe;IACpB,MAAM,QAAQ,MAAM,aAAuB;IAC3C,uDAAuD;IACvD,OAAO,MACJ,MAAM,CAAC,CAAC,OAAS,KAAK,MAAM,KAAK,aACjC,IAAI,CACH,CAAC,GAAG,IACF,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;AAEvE;AAEO,eAAe;IACpB,MAAM,QAAQ,MAAM;IACpB,OAAO,MAAM,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ;AAC7C;AAEO,eAAe,kBACpB,IAAY;IAEZ,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,eAAe,EAAE,MAAM,EAAE;YACpE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM;gBAAE,YAAY;YAAI;QAC1B;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,OAAO;QACT;QAEA,MAAM,SAAgC,MAAM,SAAS,IAAI;QACzD,OAAO,OAAO,OAAO,GAAG,OAAO,IAAI,GAAG;IACxC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO;IACT;AACF;AAGO,eAAe;IACpB,MAAM,SAAS,MAAM,aAAoB;IACzC,OAAO,OACJ,MAAM,CAAC,CAAC,QAAU,MAAM,MAAM,KAAK,aACnC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AACrC;AAEO,eAAe;IACpB,MAAM,SAAS,MAAM;IACrB,OAAO,OAAO,MAAM,CAAC,CAAC,QAAU,MAAM,QAAQ;AAChD;AAGO,eAAe;IACpB,MAAM,QAAQ,MAAM,aAAmB;IACvC,OAAO,MACJ,MAAM,CAAC,CAAC,OAAS,KAAK,MAAM,KAAK,aACjC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AACrC;AAEO,eAAe;IACpB,MAAM,QAAQ,MAAM;IACpB,OAAO,MAAM,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ;AAC7C;AAGO,eAAe;IACpB,MAAM,eAAe,MAAM,aAA0B;IACrD,OAAO,aACJ,MAAM,CAAC,CAAC,cAAgB,YAAY,MAAM,KAAK,aAC/C,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AACrC;AAEO,eAAe;IACpB,MAAM,eAAe,MAAM;IAC3B,OAAO,aAAa,MAAM,CAAC,CAAC,cAAgB,YAAY,QAAQ;AAClE;AAGO,eAAe;IACpB,MAAM,UAAU,MAAM,aAAqB;IAC3C,OAAO,QACJ,MAAM,CAAC,CAAC,SAAW,OAAO,MAAM,KAAK,aACrC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AACrC;AAEO,eAAe;IACpB,MAAM,UAAU,MAAM;IACtB,OAAO,QAAQ,MAAM,CAAC,CAAC,SAAW,OAAO,QAAQ;AACnD;AAGO,SAAS,oBAAoB,OAAe;IACjD,OAAO,CAAC,2BAA2B,EAAE,QAAQ,kBAAkB,CAAC;AAClE;AAEO,SAAS,iBAAiB,EAAU;IACzC,wCAAwC;IACxC,OAAO,CAAC,8BAA8B,EAAE,IAAI;AAC9C;AAEO,SAAS,0BAA0B,OAAe;IACvD,OAAO,CAAC,2BAA2B,EAAE,QAAQ,kBAAkB,CAAC;AAClE;AAGO,SAAS,oBAAoB,IAAU;IAC5C,OAAO;QACL,GAAG,IAAI;QACP,WAAW,KAAK,SAAS,IAAI,0BAA0B,KAAK,EAAE;IAChE;AACF;AAEO,SAAS,WAAW,UAAkB;IAC3C,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;QACtD,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,kBAAkB,OAAe;IAC/C,MAAM,iBAAiB;IACvB,MAAM,YAAY,QAAQ,KAAK,CAAC,OAAO,MAAM;IAC7C,OAAO,KAAK,IAAI,CAAC,YAAY;AAC/B", "debugId": null}}, {"offset": {"line": 693, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/FeaturedWork.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Image from 'next/image';\nimport Link from 'next/link';\nimport { Play, X } from 'lucide-react';\nimport { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';\nimport { Badge } from '@/components/ui/badge';\nimport { getFeaturedVideos, getYouTubeThumbnail, type Video } from '@/lib/api';\n\nexport default function FeaturedWork() {\n  const [selectedVideo, setSelectedVideo] = useState<string | null>(null);\n  const [videos, setVideos] = useState<Video[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const fetchVideos = async () => {\n      try {\n        const featuredVideos = await getFeaturedVideos();\n        // Show only first 6 videos for featured section\n        setVideos(featuredVideos.slice(0, 6));\n      } catch (error) {\n        console.error('Error fetching featured videos:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchVideos();\n  }, []);\n\n  const getYouTubeEmbedUrl = (videoId: string) => {\n    return `https://www.youtube.com/embed/${videoId}?autoplay=1&rel=0&modestbranding=1`;\n  };\n\n  return (\n    <section id=\"featured-work\" className=\"py-20 bg-background\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-heading font-bold text-foreground mb-4\">\n            Featured Work\n          </h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n            A selection of projects showcasing diverse editing styles and creative storytelling techniques.\n          </p>\n        </div>\n\n        {loading ? (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {[...Array(6)].map((_, index) => (\n              <div key={index} className=\"bg-card rounded-xl overflow-hidden shadow-lg\">\n                <div className=\"aspect-video bg-gray-200 animate-pulse\"></div>\n                <div className=\"p-6\">\n                  <div className=\"h-6 bg-gray-200 rounded animate-pulse mb-2\"></div>\n                  <div className=\"h-4 bg-gray-200 rounded animate-pulse w-3/4\"></div>\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : videos.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <p className=\"text-lg text-muted-foreground\">No featured videos available at the moment.</p>\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {videos.map((video, index) => (\n              <div\n                key={video._id}\n                className=\"group bg-card rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105\"\n              >\n              <div className=\"relative aspect-video overflow-hidden\">\n                <Image\n                  src={getYouTubeThumbnail(video.id)}\n                  alt={video.title}\n                  fill\n                  className=\"object-cover transition-transform duration-500 group-hover:scale-110\"\n                />\n\n                {/* Play Button Overlay */}\n                <Dialog>\n                  <DialogTrigger asChild>\n                    <button\n                      className=\"absolute inset-0 flex items-center justify-center bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                      onClick={() => setSelectedVideo(video.id)}\n                    >\n                      <div className=\"w-16 h-16 bg-portfolio-secondary hover:bg-portfolio-secondary/90 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 shadow-lg\">\n                        <Play size={24} className=\"text-white ml-1\" fill=\"currentColor\" />\n                      </div>\n                    </button>\n                  </DialogTrigger>\n                  <DialogContent className=\"max-w-4xl w-full p-0 bg-black border-0\">\n                    <div className=\"relative aspect-video\">\n                        {/* <button\n                          onClick={() => setSelectedVideo(null)}\n                          className=\"absolute -top-12 right-0 z-50 text-white hover:text-portfolio-accent transition-colors\"\n                        >\n                          <X size={24} />\n                        </button> */}\n                      {selectedVideo === video.id && (\n                        <iframe\n                          src={getYouTubeEmbedUrl(video.id)}\n                          title={video.title}\n                          className=\"w-full h-full\"\n                          allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n                          allowFullScreen\n                        />\n                      )}\n                    </div>\n                  </DialogContent>\n                </Dialog>\n\n                {/* Category Badge */}\n                {video.category && (\n                  <div className=\"absolute top-4 left-4\">\n                    <Badge variant=\"secondary\" className=\"bg-portfolio-accent/90 text-white\">\n                      {video.category}\n                    </Badge>\n                  </div>\n                )}\n              </div>\n\n              <div className=\"p-6\">\n                <h3 className=\"text-xl font-heading font-semibold text-foreground mb-2 group-hover:text-accent transition-colors duration-300\">\n                  {video.title}\n                </h3>\n                {video.description && (\n                  <p className=\"text-muted-foreground text-sm leading-relaxed\">\n                    {video.description}\n                  </p>\n                )}\n              </div>\n            </div>\n            ))}\n          </div>\n        )}\n\n        {/* View More Button */}\n        <div className=\"text-center mt-12\">\n          <Link href=\"/videos\">\n            <button className=\"bg-accent hover:bg-accent/90 text-accent-foreground px-8 py-3 rounded-full font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg border border-accent/20\">\n              View All Videos\n            </button>\n          </Link>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAUe,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc;YAClB,IAAI;gBACF,MAAM,iBAAiB,MAAM,CAAA,GAAA,iHAAA,CAAA,oBAAiB,AAAD;gBAC7C,gDAAgD;gBAChD,UAAU,eAAe,KAAK,CAAC,GAAG;YACpC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;YACnD,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,qBAAqB,CAAC;QAC1B,OAAO,CAAC,8BAA8B,EAAE,QAAQ,kCAAkC,CAAC;IACrF;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAgB,WAAU;kBACpC,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmE;;;;;;sCAGjF,8OAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;gBAKhE,wBACC,8OAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,sBACrB,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;2BAJT;;;;;;;;;2BASZ,OAAO,MAAM,KAAK,kBACpB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAgC;;;;;;;;;;yCAG/C,8OAAC;oBAAI,WAAU;8BACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC;4BAEC,WAAU;;8CAEZ,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAK,CAAA,GAAA,iHAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,EAAE;4CACjC,KAAK,MAAM,KAAK;4CAChB,IAAI;4CACJ,WAAU;;;;;;sDAIZ,8OAAC,kIAAA,CAAA,SAAM;;8DACL,8OAAC,kIAAA,CAAA,gBAAa;oDAAC,OAAO;8DACpB,cAAA,8OAAC;wDACC,WAAU;wDACV,SAAS,IAAM,iBAAiB,MAAM,EAAE;kEAExC,cAAA,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gEAAC,MAAM;gEAAI,WAAU;gEAAkB,MAAK;;;;;;;;;;;;;;;;;;;;;8DAIvD,8OAAC,kIAAA,CAAA,gBAAa;oDAAC,WAAU;8DACvB,cAAA,8OAAC;wDAAI,WAAU;kEAOZ,kBAAkB,MAAM,EAAE,kBACzB,8OAAC;4DACC,KAAK,mBAAmB,MAAM,EAAE;4DAChC,OAAO,MAAM,KAAK;4DAClB,WAAU;4DACV,OAAM;4DACN,eAAe;;;;;;;;;;;;;;;;;;;;;;wCAQxB,MAAM,QAAQ,kBACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAY,WAAU;0DAClC,MAAM,QAAQ;;;;;;;;;;;;;;;;;8CAMvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDACX,MAAM,KAAK;;;;;;wCAEb,MAAM,WAAW,kBAChB,8OAAC;4CAAE,WAAU;sDACV,MAAM,WAAW;;;;;;;;;;;;;2BA5DjB,MAAM,GAAG;;;;;;;;;;8BAsEtB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;kCACT,cAAA,8OAAC;4BAAO,WAAU;sCAA+K;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7M", "debugId": null}}, {"offset": {"line": 1006, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/ClientCard.tsx"], "sourcesContent": ["import Image from 'next/image';\nimport { ExternalLink } from 'lucide-react';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { type Client } from '@/lib/api';\nimport clsx from 'clsx';\n\ninterface ClientCardProps {\n  client: Client;\n  variant?: 'logo' | 'detailed' | 'compact';\n  onClick?: () => void;\n  showWebsite?: boolean;\n  className?: string;\n}\n\nconst cardStyles = {\n  logo: 'group relative bg-card p-4 rounded-xl shadow-md hover:shadow-xl transition-all duration-300 hover:scale-105 cursor-pointer w-full h-full flex items-center justify-center',\n  detailed: 'bg-card rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105',\n  compact: 'bg-card rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-all duration-300'\n};\n\nconst imageSizes = {\n  logo: { className: 'relative h-16 w-full max-w-[120px] mx-auto', size: '16vw' },\n  detailed: { className: 'relative h-24 w-full max-w-[200px] mx-auto mb-6', size: '33vw' },\n  compact: { className: 'relative h-12 w-full max-w-[100px] mx-auto mb-3', size: '33vw' }\n};\n\nconst contentPadding = {\n  detailed: 'p-6 text-center',\n  compact: 'p-4'\n};\n\nfunction ClientImage({ client, variant }: { client: Client; variant: keyof typeof imageSizes }) {\n  return (\n    <div className={imageSizes[variant].className}>\n      <Image\n        src={client.logo}\n        alt={client.name}\n        fill\n        className={clsx('object-contain', variant === 'detailed' && 'rounded-md')}\n        sizes={imageSizes[variant].size}\n      />\n    </div>\n  );\n}\n\nfunction ClientBadges({ client }: { client: Client }) {\n  return (\n    <div className=\"flex flex-wrap justify-center gap-2 mb-4\">\n      {client.industry && <Badge variant=\"outline\">{client.industry}</Badge>}\n      {client.projectType && <Badge variant=\"secondary\">{client.projectType}</Badge>}\n    </div>\n  );\n}\n\nexport default function ClientCard({\n  client,\n  variant = 'logo',\n  onClick,\n  showWebsite = false,\n  className = ''\n}: ClientCardProps) {\n  if (variant === 'logo') {\n    return (\n      <button\n        onClick={onClick}\n        className={clsx(\n          'group bg-card rounded-xl shadow-md hover:shadow-xl transition-all duration-300 hover:scale-105 cursor-pointer w-40 h-40 flex flex-col items-center justify-center text-center',\n          className\n        )}\n        title={client.name}\n        aria-label={`View ${client.name}`}\n      >\n        <div className=\"relative w-24 h-24 mb-4\">\n          <Image\n            src={client.logo}\n            alt={client.name}\n            fill\n            className=\"object-cover rounded-full border border-gray-200 shadow-sm\"\n            sizes=\"96px\"\n          />\n        </div>\n        <p className=\"text-sm font-medium text-gray-700\">{client.name}</p>\n      </button>\n    );\n  }\n  if (variant === 'detailed') {\n    return (\n      <article\n        className={clsx(\n          'bg-card rounded-xl shadow-md hover:shadow-lg transition-all duration-300 w-full max-w-md overflow-hidden',\n          className\n        )}\n      >\n        <div className=\"p-6 flex flex-col items-center text-center\">\n          <div className=\"relative w-28 h-28 mb-4\">\n            <Image\n              src={client.logo}\n              alt={client.name}\n              fill\n              className=\"object-cover rounded-xl border border-gray-200 shadow-sm\"\n              sizes=\"112px\"\n            />\n          </div>\n\n          <h3 className=\"text-2xl font-heading font-bold text-foreground mb-2\">\n            {client.name}\n          </h3>\n\n          <div className=\"flex flex-wrap justify-center gap-2 mb-4\">\n            {client.industry && <Badge variant=\"outline\">{client.industry}</Badge>}\n            {client.projectType && <Badge variant=\"secondary\">{client.projectType}</Badge>}\n          </div>\n\n          <p className=\"text-muted-foreground leading-relaxed mb-6\">\n            {client.description}\n          </p>\n\n          {showWebsite && client.website && (\n            <Button variant=\"outline\" asChild>\n              <a\n                href={client.website}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"inline-flex items-center gap-2\"\n              >\n                Visit Website <ExternalLink size={16} />\n              </a>\n            </Button>\n          )}\n        </div>\n      </article>\n    );\n  }\n\n\n  // Compact\n  return (\n    <article className={clsx(cardStyles.compact, className)}>\n      <div className={contentPadding.compact}>\n        <ClientImage client={client} variant=\"compact\" />\n        <h4 className=\"text-lg font-heading font-semibold text-foreground mb-2\">{client.name}</h4>\n        <ClientBadges client={client} />\n        <p className=\"text-muted-foreground text-sm leading-relaxed mb-4 line-clamp-3\">\n          {client.description}\n        </p>\n        <div className=\"flex items-center justify-between\">\n          {onClick && (\n            <button\n              onClick={onClick}\n              className=\"text-foreground font-semibold text-sm hover:text-accent transition-colors duration-300\"\n              aria-label={`Learn more about ${client.name}`}\n            >\n              Learn More →\n            </button>\n          )}\n          {showWebsite && client.website && (\n            <a\n              href={client.website}\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"text-portfolio-secondary hover:text-portfolio-primary transition-colors duration-300\"\n              aria-label={`Visit ${client.name} website`}\n            >\n              <ExternalLink size={16} />\n            </a>\n          )}\n        </div>\n      </div>\n    </article>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAEA;;;;;;;AAUA,MAAM,aAAa;IACjB,MAAM;IACN,UAAU;IACV,SAAS;AACX;AAEA,MAAM,aAAa;IACjB,MAAM;QAAE,WAAW;QAA8C,MAAM;IAAO;IAC9E,UAAU;QAAE,WAAW;QAAmD,MAAM;IAAO;IACvF,SAAS;QAAE,WAAW;QAAmD,MAAM;IAAO;AACxF;AAEA,MAAM,iBAAiB;IACrB,UAAU;IACV,SAAS;AACX;AAEA,SAAS,YAAY,EAAE,MAAM,EAAE,OAAO,EAAwD;IAC5F,qBACE,8OAAC;QAAI,WAAW,UAAU,CAAC,QAAQ,CAAC,SAAS;kBAC3C,cAAA,8OAAC,6HAAA,CAAA,UAAK;YACJ,KAAK,OAAO,IAAI;YAChB,KAAK,OAAO,IAAI;YAChB,IAAI;YACJ,WAAW,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EAAE,kBAAkB,YAAY,cAAc;YAC5D,OAAO,UAAU,CAAC,QAAQ,CAAC,IAAI;;;;;;;;;;;AAIvC;AAEA,SAAS,aAAa,EAAE,MAAM,EAAsB;IAClD,qBACE,8OAAC;QAAI,WAAU;;YACZ,OAAO,QAAQ,kBAAI,8OAAC,iIAAA,CAAA,QAAK;gBAAC,SAAQ;0BAAW,OAAO,QAAQ;;;;;;YAC5D,OAAO,WAAW,kBAAI,8OAAC,iIAAA,CAAA,QAAK;gBAAC,SAAQ;0BAAa,OAAO,WAAW;;;;;;;;;;;;AAG3E;AAEe,SAAS,WAAW,EACjC,MAAM,EACN,UAAU,MAAM,EAChB,OAAO,EACP,cAAc,KAAK,EACnB,YAAY,EAAE,EACE;IAChB,IAAI,YAAY,QAAQ;QACtB,qBACE,8OAAC;YACC,SAAS;YACT,WAAW,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EACZ,iLACA;YAEF,OAAO,OAAO,IAAI;YAClB,cAAY,CAAC,KAAK,EAAE,OAAO,IAAI,EAAE;;8BAEjC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAK,OAAO,IAAI;wBAChB,KAAK,OAAO,IAAI;wBAChB,IAAI;wBACJ,WAAU;wBACV,OAAM;;;;;;;;;;;8BAGV,8OAAC;oBAAE,WAAU;8BAAqC,OAAO,IAAI;;;;;;;;;;;;IAGnE;IACA,IAAI,YAAY,YAAY;QAC1B,qBACE,8OAAC;YACC,WAAW,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EACZ,4GACA;sBAGF,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAK,OAAO,IAAI;4BAChB,KAAK,OAAO,IAAI;4BAChB,IAAI;4BACJ,WAAU;4BACV,OAAM;;;;;;;;;;;kCAIV,8OAAC;wBAAG,WAAU;kCACX,OAAO,IAAI;;;;;;kCAGd,8OAAC;wBAAI,WAAU;;4BACZ,OAAO,QAAQ,kBAAI,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAW,OAAO,QAAQ;;;;;;4BAC5D,OAAO,WAAW,kBAAI,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAa,OAAO,WAAW;;;;;;;;;;;;kCAGvE,8OAAC;wBAAE,WAAU;kCACV,OAAO,WAAW;;;;;;oBAGpB,eAAe,OAAO,OAAO,kBAC5B,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,OAAO;kCAC/B,cAAA,8OAAC;4BACC,MAAM,OAAO,OAAO;4BACpB,QAAO;4BACP,KAAI;4BACJ,WAAU;;gCACX;8CACe,8OAAC,sNAAA,CAAA,eAAY;oCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOhD;IAGA,UAAU;IACV,qBACE,8OAAC;QAAQ,WAAW,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EAAE,WAAW,OAAO,EAAE;kBAC3C,cAAA,8OAAC;YAAI,WAAW,eAAe,OAAO;;8BACpC,8OAAC;oBAAY,QAAQ;oBAAQ,SAAQ;;;;;;8BACrC,8OAAC;oBAAG,WAAU;8BAA2D,OAAO,IAAI;;;;;;8BACpF,8OAAC;oBAAa,QAAQ;;;;;;8BACtB,8OAAC;oBAAE,WAAU;8BACV,OAAO,WAAW;;;;;;8BAErB,8OAAC;oBAAI,WAAU;;wBACZ,yBACC,8OAAC;4BACC,SAAS;4BACT,WAAU;4BACV,cAAY,CAAC,iBAAiB,EAAE,OAAO,IAAI,EAAE;sCAC9C;;;;;;wBAIF,eAAe,OAAO,OAAO,kBAC5B,8OAAC;4BACC,MAAM,OAAO,OAAO;4BACpB,QAAO;4BACP,KAAI;4BACJ,WAAU;4BACV,cAAY,CAAC,MAAM,EAAE,OAAO,IAAI,CAAC,QAAQ,CAAC;sCAE1C,cAAA,8OAAC,sNAAA,CAAA,eAAY;gCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlC", "debugId": null}}, {"offset": {"line": 1329, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/Clients.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { ChevronLeft, ChevronRight } from 'lucide-react';\nimport { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';\nimport { Button } from '@/components/ui/button';\nimport ClientCard from '@/components/ClientCard';\nimport { getFeaturedClients, type Client } from '@/lib/api';\n\nexport default function Clients() {\n  const [selectedClient, setSelectedClient] = useState<string | null>(null);\n  const [clients, setClients] = useState<Client[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const fetchClients = async () => {\n      try {\n        const featuredClients = await getFeaturedClients();\n        setClients(featuredClients);\n      } catch (error) {\n        console.error('Error fetching clients:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchClients();\n  }, []);\n\n  const openClientModal = (clientId: string) => {\n    setSelectedClient(clientId);\n  };\n\n  const navigateClient = (direction: 'prev' | 'next') => {\n    if (selectedClient === null) return;\n\n    const currentIndex = clients.findIndex(client => client._id === selectedClient);\n    let newIndex;\n\n    if (direction === 'prev') {\n      newIndex = currentIndex > 0 ? currentIndex - 1 : clients.length - 1;\n    } else {\n      newIndex = currentIndex < clients.length - 1 ? currentIndex + 1 : 0;\n    }\n\n    setSelectedClient(clients[newIndex]._id);\n  };\n\n  const selectedClientData = clients.find(client => client._id === selectedClient);\n\n  return (\n    <section id=\"clients\" className=\"py-20 bg-secondary/30\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-heading font-bold text-foreground mb-4\">\n            Trusted By\n          </h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n            Proud to have collaborated with diverse clients across various industries.\n          </p>\n        </div>\n\n        {loading ? (\n          <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6 items-stretch\">\n            {[...Array(12)].map((_, index) => (\n              <div key={index} className=\"h-24 flex items-center justify-center\">\n                <div className=\"bg-card p-4 rounded-xl shadow-md w-full h-full flex items-center justify-center min-h-[80px]\">\n                  <div className=\"h-12 w-20 bg-muted rounded animate-pulse\"></div>\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : clients.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <p className=\"text-lg text-muted-foreground\">No clients available at the moment.</p>\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6 items-stretch\">\n            {clients.map((client) => (\n              <Dialog key={client._id}>\n                <DialogTrigger asChild>\n                  <div className=\"h-24 flex items-center justify-center\">\n                    <ClientCard\n                      client={client}\n                      variant=\"logo\"\n                      onClick={() => openClientModal(client._id)}\n                      className=\"min-h-[80px]\"\n                    />\n                  </div>\n                </DialogTrigger>\n              <DialogContent className=\"max-w-2xl\">\n                <div className=\"relative\">\n                  {/* Navigation Arrows */}\n                  <Button\n                    variant=\"ghost\"\n                    size=\"icon\"\n                    className=\"absolute -left-12 top-1/2 -translate-y-1/2 z-10\"\n                    onClick={() => navigateClient('prev')}\n                  >\n                    <ChevronLeft size={20} />\n                  </Button>\n                  <Button\n                    variant=\"ghost\"\n                    size=\"icon\"\n                    className=\"absolute -right-12 top-1/2 -translate-y-1/2 z-10\"\n                    onClick={() => navigateClient('next')}\n                  >\n                    <ChevronRight size={20} />\n                  </Button>\n\n                  {selectedClientData && (\n                    <ClientCard\n                      client={selectedClientData}\n                      variant=\"detailed\"\n                      showWebsite={true}\n                    />\n                  )}\n                </div>\n              </DialogContent>\n            </Dialog>\n            ))}\n          </div>\n        )}\n\n        {/* View All Clients Button */}\n        <div className=\"text-center mt-12\">\n          <Link href=\"/clients\">\n            <Button className=\"bg-accent hover:bg-accent/90 text-accent-foreground px-8 py-3 rounded-full font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg border border-accent/20\">\n              View All Clients\n            </Button>\n          </Link>\n        </div>\n\n        {/* Stats Section */}\n        <div className=\"mt-20 grid grid-cols-2 md:grid-cols-4 gap-8\">\n          {[\n            { number: '50+', label: 'Projects Completed' },\n            { number: '25+', label: 'Happy Clients' },\n            { number: '1M+', label: 'Views Generated' },\n            { number: '98%', label: 'Client Satisfaction' },\n          ].map((stat, index) => (\n            <div key={index} className=\"text-center\">\n              <div className=\"text-3xl md:text-4xl font-heading font-bold text-portfolio-secondary mb-2\">\n                {stat.number}\n              </div>\n              <div className=\"text-muted-foreground font-medium\">\n                {stat.label}\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAUe,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,IAAI;gBACF,MAAM,kBAAkB,MAAM,CAAA,GAAA,iHAAA,CAAA,qBAAkB,AAAD;gBAC/C,WAAW;YACb,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;YAC3C,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAC;QACvB,kBAAkB;IACpB;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,mBAAmB,MAAM;QAE7B,MAAM,eAAe,QAAQ,SAAS,CAAC,CAAA,SAAU,OAAO,GAAG,KAAK;QAChE,IAAI;QAEJ,IAAI,cAAc,QAAQ;YACxB,WAAW,eAAe,IAAI,eAAe,IAAI,QAAQ,MAAM,GAAG;QACpE,OAAO;YACL,WAAW,eAAe,QAAQ,MAAM,GAAG,IAAI,eAAe,IAAI;QACpE;QAEA,kBAAkB,OAAO,CAAC,SAAS,CAAC,GAAG;IACzC;IAEA,MAAM,qBAAqB,QAAQ,IAAI,CAAC,CAAA,SAAU,OAAO,GAAG,KAAK;IAEjE,qBACE,8OAAC;QAAQ,IAAG;QAAU,WAAU;kBAC9B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmE;;;;;;sCAGjF,8OAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;gBAKhE,wBACC,8OAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,sBACtB,8OAAC;4BAAgB,WAAU;sCACzB,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;;;;;;;;;;2BAFT;;;;;;;;;2BAOZ,QAAQ,MAAM,KAAK,kBACrB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAgC;;;;;;;;;;yCAG/C,8OAAC;oBAAI,WAAU;8BACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,kIAAA,CAAA,SAAM;;8CACL,8OAAC,kIAAA,CAAA,gBAAa;oCAAC,OAAO;8CACpB,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,gIAAA,CAAA,UAAU;4CACT,QAAQ;4CACR,SAAQ;4CACR,SAAS,IAAM,gBAAgB,OAAO,GAAG;4CACzC,WAAU;;;;;;;;;;;;;;;;8CAIlB,8OAAC,kIAAA,CAAA,gBAAa;oCAAC,WAAU;8CACvB,cAAA,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,eAAe;0DAE9B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oDAAC,MAAM;;;;;;;;;;;0DAErB,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,eAAe;0DAE9B,cAAA,8OAAC,sNAAA,CAAA,eAAY;oDAAC,MAAM;;;;;;;;;;;4CAGrB,oCACC,8OAAC,gIAAA,CAAA,UAAU;gDACT,QAAQ;gDACR,SAAQ;gDACR,aAAa;;;;;;;;;;;;;;;;;;2BAnCR,OAAO,GAAG;;;;;;;;;;8BA8C7B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;kCACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,WAAU;sCAA+K;;;;;;;;;;;;;;;;8BAOrM,8OAAC;oBAAI,WAAU;8BACZ;wBACC;4BAAE,QAAQ;4BAAO,OAAO;wBAAqB;wBAC7C;4BAAE,QAAQ;4BAAO,OAAO;wBAAgB;wBACxC;4BAAE,QAAQ;4BAAO,OAAO;wBAAkB;wBAC1C;4BAAE,QAAQ;4BAAO,OAAO;wBAAsB;qBAC/C,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCAAI,WAAU;8CACZ,KAAK,MAAM;;;;;;8CAEd,8OAAC;oCAAI,WAAU;8CACZ,KAAK,KAAK;;;;;;;2BALL;;;;;;;;;;;;;;;;;;;;;AAatB", "debugId": null}}, {"offset": {"line": 1643, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/Reels.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Image from 'next/image';\nimport Link from 'next/link';\nimport { Play, X, ChevronLeft, ChevronRight } from 'lucide-react';\nimport { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { getFeaturedReels, type Reel, getYouTubeShortsThumbnail } from '@/lib/api';\n\nexport default function Reels() {\n  const [selectedReel, setSelectedReel] = useState<string | null>(null);\n  const [reels, setReels] = useState<Reel[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const fetchReels = async () => {\n      try {\n        const featuredReels = await getFeaturedReels();\n        console.log(featuredReels);\n        // Show only first 12 shorts for featured section\n        const shortsWithThumbnails = featuredReels.slice(0, 12).map(reel => ({\n          ...reel,\n          thumbnail: reel.thumbnail || getYouTubeShortsThumbnail(reel.id)\n        }));\n        setReels(shortsWithThumbnails);\n      } catch (error) {\n        console.error('Error fetching featured shorts:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchReels();\n  }, []);\n\n  const openReelModal = (reelId: string) => {\n    setSelectedReel(reelId);\n  };\n\n  const navigateReel = (direction: 'prev' | 'next') => {\n    if (!selectedReel) return;\n\n    const currentIndex = reels.findIndex(reel => reel.id === selectedReel);\n    let newIndex;\n\n    if (direction === 'prev') {\n      newIndex = currentIndex > 0 ? currentIndex - 1 : reels.length - 1;\n    } else {\n      newIndex = currentIndex < reels.length - 1 ? currentIndex + 1 : 0;\n    }\n\n    setSelectedReel(reels[newIndex].id);\n  };\n\n  const selectedReelData = reels.find(reel => reel.id === selectedReel);\n\n  // if(!loading && reels.length !== 0) {\n  //   return console.log(reels);\n  // }\n\n  return (\n    <section id=\"shorts\" className=\"py-20 bg-background\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-heading font-bold text-foreground mb-4\">\n            YouTube Shorts\n          </h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto font-body\">\n            Creative short-form content showcasing dynamic editing and storytelling.\n          </p>\n        </div>\n\n        {loading ? (\n          <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6\">\n            {[...Array(12)].map((_, index) => (\n              <div key={index} className=\"bg-card rounded-xl overflow-hidden shadow-lg\">\n                <div className=\"aspect-[9/16] bg-gray-200 animate-pulse\"></div>\n                <div className=\"p-3\">\n                  <div className=\"h-4 bg-gray-200 rounded animate-pulse mb-1\"></div>\n                  <div className=\"h-3 bg-gray-200 rounded animate-pulse w-3/4\"></div>\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : reels.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <p className=\"text-lg text-muted-foreground font-body\">No YouTube Shorts available at the moment.</p>\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6\">\n            {reels.map((reel) => (\n              <div\n                key={reel._id}\n                className=\"group bg-card rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105\"\n              >\n                <div className=\"relative aspect-[9/16] overflow-hidden\">\n                  <Image\n                    src={reel.thumbnail}\n                    alt={reel.title}\n                    fill\n                    className=\"object-cover transition-transform duration-500 group-hover:scale-110\"\n                  />\n\n                  {/* Play Button Overlay */}\n                  <Dialog>\n                    <DialogTrigger asChild>\n                      <button\n                        className=\"absolute inset-0 flex items-center justify-center bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                        onClick={() => openReelModal(reel.id)}\n                      >\n                        <div className=\"w-12 h-12 bg-accent hover:bg-accent/90 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 shadow-lg\">\n                          <Play size={16} className=\"text-accent-foreground ml-0.5\" fill=\"currentColor\" />\n                        </div>\n                      </button>\n                    </DialogTrigger>\n                    <DialogContent className=\"max-w-md w-full p-0 bg-black border-0\">\n                      <div className=\"relative\">\n                        {/* Navigation Arrows */}\n                        <Button\n                          variant=\"ghost\"\n                          size=\"icon\"\n                          className=\"absolute -left-16 top-1/2 -translate-y-1/2 z-10 text-white hover:text-accent\"\n                          onClick={() => navigateReel('prev')}\n                        >\n                          <ChevronLeft size={20} />\n                        </Button>\n                        <Button\n                          variant=\"ghost\"\n                          size=\"icon\"\n                          className=\"absolute -right-16 top-1/2 -translate-y-1/2 z-10 text-white hover:text-accent\"\n                          onClick={() => navigateReel('next')}\n                        >\n                          <ChevronRight size={20} />\n                        </Button>\n\n                        <button\n                          onClick={() => setSelectedReel(null)}\n                          className=\"absolute -top-12 right-0 z-50 text-white hover:text-accent transition-colors\"\n                        >\n                          <X size={24} />\n                        </button>\n\n                        <div className=\"aspect-[9/16] max-h-[80vh]\">\n                          {selectedReel === reel.id && selectedReelData?.embedUrl && (\n                            <iframe\n                              src={selectedReelData.embedUrl}\n                              title={selectedReelData.title}\n                              className=\"w-full h-full\"\n                              allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n                              allowFullScreen\n                            />\n                          )}\n                        </div>\n                      </div>\n                    </DialogContent>\n                  </Dialog>\n\n                  {/* Platform Badge */}\n                  <div className=\"absolute top-2 left-2\">\n                    <Badge variant=\"secondary\" className=\"bg-accent/90 text-accent-foreground text-xs font-accent\">\n                      YouTube Shorts\n                    </Badge>\n                  </div>\n                </div>\n\n                <div className=\"p-3\">\n                  <h3 className=\"text-sm font-heading font-semibold text-foreground mb-1 group-hover:text-accent transition-colors duration-300 line-clamp-2\">\n                    {reel.title}\n                  </h3>\n                  {reel.description && (\n                    <p className=\"text-muted-foreground text-xs leading-relaxed line-clamp-2 font-body\">\n                      {reel.description}\n                    </p>\n                  )}\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n\n        {/* View More Button */}\n        <div className=\"text-center mt-12\">\n          <Link href=\"/reels\">\n            <button className=\"bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-3 rounded-full font-semibold font-accent transition-all duration-300 hover:scale-105 hover:shadow-lg\">\n              View All YouTube Shorts\n            </button>\n          </Link>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAWe,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa;YACjB,IAAI;gBACF,MAAM,gBAAgB,MAAM,CAAA,GAAA,iHAAA,CAAA,mBAAgB,AAAD;gBAC3C,QAAQ,GAAG,CAAC;gBACZ,iDAAiD;gBACjD,MAAM,uBAAuB,cAAc,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAA,OAAQ,CAAC;wBACnE,GAAG,IAAI;wBACP,WAAW,KAAK,SAAS,IAAI,CAAA,GAAA,iHAAA,CAAA,4BAAyB,AAAD,EAAE,KAAK,EAAE;oBAChE,CAAC;gBACD,SAAS;YACX,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;YACnD,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB,CAAC;QACrB,gBAAgB;IAClB;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,CAAC,cAAc;QAEnB,MAAM,eAAe,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QACzD,IAAI;QAEJ,IAAI,cAAc,QAAQ;YACxB,WAAW,eAAe,IAAI,eAAe,IAAI,MAAM,MAAM,GAAG;QAClE,OAAO;YACL,WAAW,eAAe,MAAM,MAAM,GAAG,IAAI,eAAe,IAAI;QAClE;QAEA,gBAAgB,KAAK,CAAC,SAAS,CAAC,EAAE;IACpC;IAEA,MAAM,mBAAmB,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAExD,uCAAuC;IACvC,+BAA+B;IAC/B,IAAI;IAEJ,qBACE,8OAAC;QAAQ,IAAG;QAAS,WAAU;kBAC7B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmE;;;;;;sCAGjF,8OAAC;4BAAE,WAAU;sCAA4D;;;;;;;;;;;;gBAK1E,wBACC,8OAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,sBACtB,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;2BAJT;;;;;;;;;2BASZ,MAAM,MAAM,KAAK,kBACnB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAA0C;;;;;;;;;;yCAGzD,8OAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;4BAEC,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAK,KAAK,SAAS;4CACnB,KAAK,KAAK,KAAK;4CACf,IAAI;4CACJ,WAAU;;;;;;sDAIZ,8OAAC,kIAAA,CAAA,SAAM;;8DACL,8OAAC,kIAAA,CAAA,gBAAa;oDAAC,OAAO;8DACpB,cAAA,8OAAC;wDACC,WAAU;wDACV,SAAS,IAAM,cAAc,KAAK,EAAE;kEAEpC,cAAA,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gEAAC,MAAM;gEAAI,WAAU;gEAAgC,MAAK;;;;;;;;;;;;;;;;;;;;;8DAIrE,8OAAC,kIAAA,CAAA,gBAAa;oDAAC,WAAU;8DACvB,cAAA,8OAAC;wDAAI,WAAU;;0EAEb,8OAAC,kIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,SAAS,IAAM,aAAa;0EAE5B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oEAAC,MAAM;;;;;;;;;;;0EAErB,8OAAC,kIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,SAAS,IAAM,aAAa;0EAE5B,cAAA,8OAAC,sNAAA,CAAA,eAAY;oEAAC,MAAM;;;;;;;;;;;0EAGtB,8OAAC;gEACC,SAAS,IAAM,gBAAgB;gEAC/B,WAAU;0EAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oEAAC,MAAM;;;;;;;;;;;0EAGX,8OAAC;gEAAI,WAAU;0EACZ,iBAAiB,KAAK,EAAE,IAAI,kBAAkB,0BAC7C,8OAAC;oEACC,KAAK,iBAAiB,QAAQ;oEAC9B,OAAO,iBAAiB,KAAK;oEAC7B,WAAU;oEACV,OAAM;oEACN,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAS3B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAY,WAAU;0DAA0D;;;;;;;;;;;;;;;;;8CAMnG,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDACX,KAAK,KAAK;;;;;;wCAEZ,KAAK,WAAW,kBACf,8OAAC;4CAAE,WAAU;sDACV,KAAK,WAAW;;;;;;;;;;;;;2BA/ElB,KAAK,GAAG;;;;;;;;;;8BAyFrB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;kCACT,cAAA,8OAAC;4BAAO,WAAU;sCAAsK;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpM", "debugId": null}}, {"offset": {"line": 2039, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/PostCard.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport Image from 'next/image';\nimport { Calendar, Clock, User } from 'lucide-react';\nimport { Badge } from '@/components/ui/badge';\nimport { formatDate, type BlogPost } from '@/lib/api';\n\ninterface PostCardProps {\n  post: BlogPost;\n  variant?: 'default' | 'featured' | 'compact';\n  showExcerpt?: boolean;\n  showAuthor?: boolean;\n  className?: string;\n}\n\nexport default function PostCard({\n  post,\n  variant = 'default',\n  showExcerpt = true,\n  showAuthor = true,\n  className = ''\n}: PostCardProps) {\n  const cardClasses = {\n    default: 'bg-card rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105',\n    featured: 'bg-card rounded-xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-105 border-2 border-accent',\n    compact: 'bg-card rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-all duration-300'\n  };\n\n  const imageClasses = {\n    default: 'aspect-video',\n    featured: 'aspect-video',\n    compact: 'aspect-[4/3]'\n  };\n\n  const contentClasses = {\n    default: 'p-6',\n    featured: 'p-8',\n    compact: 'p-4'\n  };\n\n  const titleClasses = {\n    default: 'text-xl font-heading font-semibold text-portfolio-primary mb-3',\n    featured: 'text-2xl font-heading font-bold text-portfolio-primary mb-4',\n    compact: 'text-lg font-heading font-semibold text-portfolio-primary mb-2'\n  };\n\n  return (\n    <article className={`group ${cardClasses[variant]} ${className}`}>\n      <Link href={`/blog/${post.slug}`}>\n        {/* Featured Image */}\n        <div className={`relative ${imageClasses[variant]} overflow-hidden`}>\n          {post.thumbnail ? (\n            <Image\n              src={post.thumbnail}\n              alt={post.title}\n              fill\n              className=\"object-cover transition-transform duration-500 group-hover:scale-110\"\n              sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\n            />\n          ) : (\n            <div className=\"w-full h-full bg-gradient-to-br from-primary to-accent flex items-center justify-center\">\n              <div className=\"text-white text-6xl font-heading font-bold opacity-20\">\n                {post.title.charAt(0)}\n              </div>\n            </div>\n          )}\n\n          {/* Overlay with badges */}\n          <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n\n          {/* Category Badge */}\n          <div className=\"absolute top-4 left-4\">\n            <Badge variant=\"secondary\" className=\"bg-portfolio-secondary/90 text-white backdrop-blur-sm\">\n              {post.category}\n            </Badge>\n          </div>\n\n          {/* Featured Badge */}\n          {post.featured && (\n            <div className=\"absolute top-4 right-4\">\n              <Badge variant=\"default\" className=\"bg-portfolio-accent text-white backdrop-blur-sm\">\n                Featured\n              </Badge>\n            </div>\n          )}\n        </div>\n\n        {/* Content */}\n        <div className={contentClasses[variant]}>\n          {/* Title */}\n          <h3 className={`${titleClasses[variant]} group-hover:text-portfolio-secondary transition-colors duration-300 line-clamp-2`}>\n            {post.title}\n          </h3>\n\n          {/* Meta Information */}\n          <div className=\"flex flex-wrap items-center gap-4 text-sm text-muted-foreground mb-3\">\n            <div className=\"flex items-center gap-1\">\n              <Calendar size={14} />\n              <span>{formatDate(post.createdAt)}</span>\n            </div>\n            <div className=\"flex items-center gap-1\">\n              <Clock size={14} />\n              <span>{post.readTime} min read</span>\n            </div>\n            {showAuthor && (\n              <div className=\"flex items-center gap-1\">\n                <User size={14} />\n                <span>Uttam Rimal</span>\n              </div>\n            )}\n          </div>\n\n          {/* Excerpt */}\n          {showExcerpt && post.excerpt && (\n            <p className={`text-muted-foreground leading-relaxed mb-4 ${variant === 'compact' ? 'text-sm line-clamp-2' : 'line-clamp-3'\n              }`}>\n              {post.excerpt}\n            </p>\n          )}\n\n          {/* Tags */}\n          {post.tags && post.tags.length > 0 && (\n            <div className=\"flex flex-wrap gap-2 mb-4\">\n              {post.tags.slice(0, variant === 'compact' ? 2 : 3).map((tag) => (\n                <Badge key={tag} variant=\"outline\" className=\"text-xs hover:bg-portfolio-primary hover:text-white transition-colors\">\n                  #{tag}\n                </Badge>\n              ))}\n              {post.tags.length > (variant === 'compact' ? 2 : 3) && (\n                <Badge variant=\"outline\" className=\"text-xs\">\n                  +{post.tags.length - (variant === 'compact' ? 2 : 3)}\n                </Badge>\n              )}\n            </div>\n          )}\n\n          {/* Read More Link */}\n          <div className=\"flex items-center justify-between\">\n            <span className=\"text-foreground font-semibold text-sm group-hover:text-accent transition-colors duration-300\">\n              Read More →\n            </span>\n\n            {/* Status indicator for featured variant */}\n            {variant === 'featured' && (\n              <div className=\"flex items-center gap-2\">\n                <div className={`w-2 h-2 rounded-full ${post.status === 'published' ? 'bg-green-500' : 'bg-yellow-500'\n                  }`} />\n                <span className=\"text-xs text-muted-foreground capitalize\">\n                  {post.status}\n                </span>\n              </div>\n            )}\n          </div>\n        </div>\n      </Link>\n    </article>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AACA;AACA;;;;;;;AAUe,SAAS,SAAS,EAC/B,IAAI,EACJ,UAAU,SAAS,EACnB,cAAc,IAAI,EAClB,aAAa,IAAI,EACjB,YAAY,EAAE,EACA;IACd,MAAM,cAAc;QAClB,SAAS;QACT,UAAU;QACV,SAAS;IACX;IAEA,MAAM,eAAe;QACnB,SAAS;QACT,UAAU;QACV,SAAS;IACX;IAEA,MAAM,iBAAiB;QACrB,SAAS;QACT,UAAU;QACV,SAAS;IACX;IAEA,MAAM,eAAe;QACnB,SAAS;QACT,UAAU;QACV,SAAS;IACX;IAEA,qBACE,8OAAC;QAAQ,WAAW,CAAC,MAAM,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC,EAAE,WAAW;kBAC9D,cAAA,8OAAC,4JAAA,CAAA,UAAI;YAAC,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;;8BAE9B,8OAAC;oBAAI,WAAW,CAAC,SAAS,EAAE,YAAY,CAAC,QAAQ,CAAC,gBAAgB,CAAC;;wBAChE,KAAK,SAAS,iBACb,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAK,KAAK,SAAS;4BACnB,KAAK,KAAK,KAAK;4BACf,IAAI;4BACJ,WAAU;4BACV,OAAM;;;;;iDAGR,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ,KAAK,KAAK,CAAC,MAAM,CAAC;;;;;;;;;;;sCAMzB,8OAAC;4BAAI,WAAU;;;;;;sCAGf,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;0CAClC,KAAK,QAAQ;;;;;;;;;;;wBAKjB,KAAK,QAAQ,kBACZ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;0CAAkD;;;;;;;;;;;;;;;;;8BAQ3F,8OAAC;oBAAI,WAAW,cAAc,CAAC,QAAQ;;sCAErC,8OAAC;4BAAG,WAAW,GAAG,YAAY,CAAC,QAAQ,CAAC,iFAAiF,CAAC;sCACvH,KAAK,KAAK;;;;;;sCAIb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,MAAM;;;;;;sDAChB,8OAAC;sDAAM,CAAA,GAAA,iHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,SAAS;;;;;;;;;;;;8CAElC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,MAAM;;;;;;sDACb,8OAAC;;gDAAM,KAAK,QAAQ;gDAAC;;;;;;;;;;;;;gCAEtB,4BACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,MAAM;;;;;;sDACZ,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;wBAMX,eAAe,KAAK,OAAO,kBAC1B,8OAAC;4BAAE,WAAW,CAAC,2CAA2C,EAAE,YAAY,YAAY,yBAAyB,gBACzG;sCACD,KAAK,OAAO;;;;;;wBAKhB,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,mBAC/B,8OAAC;4BAAI,WAAU;;gCACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,YAAY,YAAY,IAAI,GAAG,GAAG,CAAC,CAAC,oBACtD,8OAAC,iIAAA,CAAA,QAAK;wCAAW,SAAQ;wCAAU,WAAU;;4CAAwE;4CACjH;;uCADQ;;;;;gCAIb,KAAK,IAAI,CAAC,MAAM,GAAG,CAAC,YAAY,YAAY,IAAI,CAAC,mBAChD,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;;wCAAU;wCACzC,KAAK,IAAI,CAAC,MAAM,GAAG,CAAC,YAAY,YAAY,IAAI,CAAC;;;;;;;;;;;;;sCAO3D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAA+F;;;;;;gCAK9G,YAAY,4BACX,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAW,CAAC,qBAAqB,EAAE,KAAK,MAAM,KAAK,cAAc,iBAAiB,iBACnF;;;;;;sDACJ,8OAAC;4CAAK,WAAU;sDACb,KAAK,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9B", "debugId": null}}, {"offset": {"line": 2353, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2450, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/Blog.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport PostCard from '@/components/PostCard';\nimport { Card, CardContent, CardHeader } from '@/components/ui/card';\nimport { getBlogPosts, type BlogPost } from '@/lib/api';\n\nexport default function Blog() {\n  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const fetchBlogPosts = async () => {\n      try {\n        const posts = await getBlogPosts();\n        // Show only first 6 posts for homepage\n        setBlogPosts(posts.slice(0, 6));\n      } catch (error) {\n        console.error('Error fetching blog posts:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchBlogPosts();\n  }, []);\n\n  return (\n    <section id=\"blog\" className=\"py-20 bg-portfolio-light\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-heading font-bold text-portfolio-primary mb-4\">\n            Insights & Tips\n          </h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n            Sharing knowledge on video editing, storytelling, and creative techniques.\n          </p>\n        </div>\n\n        {loading ? (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {[...Array(6)].map((_, index) => (\n              <Card key={index} className=\"bg-card overflow-hidden\">\n                <CardHeader className=\"p-0\">\n                  <div className=\"aspect-video bg-muted animate-pulse\"></div>\n                </CardHeader>\n                <CardContent className=\"p-6\">\n                  <div className=\"space-y-3\">\n                    <div className=\"h-4 bg-muted animate-pulse\"></div>\n                    <div className=\"h-6 bg-muted animate-pulse\"></div>\n                    <div className=\"h-4 bg-muted animate-pulse w-3/4\"></div>\n                  </div>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        ) : blogPosts.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <p className=\"text-lg text-muted-foreground\">No blog posts available at the moment.</p>\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {blogPosts.map((post) => (\n              <PostCard\n                key={post._id}\n                post={post}\n                variant=\"default\"\n                showExcerpt={true}\n                showAuthor={false}\n              />\n            ))}\n          </div>\n        )}\n\n        {/* View All Posts Button */}\n        <div className=\"text-center mt-12\">\n          <Link\n            href=\"/blog\"\n            className=\"inline-block bg-accent hover:bg-accent/90 text-accent-foreground px-8 py-3 rounded-full font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg border border-accent/20\"\n          >\n            View All Posts\n          </Link>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB;YACrB,IAAI;gBACF,MAAM,QAAQ,MAAM,CAAA,GAAA,iHAAA,CAAA,eAAY,AAAD;gBAC/B,uCAAuC;gBACvC,aAAa,MAAM,KAAK,CAAC,GAAG;YAC9B,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,8BAA8B;YAC9C,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAQ,IAAG;QAAO,WAAU;kBAC3B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0E;;;;;;sCAGxF,8OAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;gBAKhE,wBACC,8OAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,sBACrB,8OAAC,gIAAA,CAAA,OAAI;4BAAa,WAAU;;8CAC1B,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,8OAAC;wCAAI,WAAU;;;;;;;;;;;8CAEjB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;2BARV;;;;;;;;;2BAcb,UAAU,MAAM,KAAK,kBACvB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAgC;;;;;;;;;;yCAG/C,8OAAC;oBAAI,WAAU;8BACZ,UAAU,GAAG,CAAC,CAAC,qBACd,8OAAC,8HAAA,CAAA,UAAQ;4BAEP,MAAM;4BACN,SAAQ;4BACR,aAAa;4BACb,YAAY;2BAJP,KAAK,GAAG;;;;;;;;;;8BAWrB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 2647, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/Testimonials.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Image from 'next/image';\nimport { Quote, Star, ChevronLeft, ChevronRight } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { getFeaturedTestimonials, type Testimonial } from '@/lib/api';\n\nexport default function Testimonials() {\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const [isAutoPlaying, setIsAutoPlaying] = useState(true);\n  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const fetchTestimonials = async () => {\n      try {\n        const featuredTestimonials = await getFeaturedTestimonials();\n        setTestimonials(featuredTestimonials);\n      } catch (error) {\n        console.error('Error fetching testimonials:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchTestimonials();\n  }, []);\n\n  useEffect(() => {\n    if (!isAutoPlaying || testimonials.length === 0) return;\n\n    const interval = setInterval(() => {\n      setCurrentIndex((prevIndex) =>\n        prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1\n      );\n    }, 6000);\n\n    return () => clearInterval(interval);\n  }, [isAutoPlaying, testimonials.length]);\n\n  const goToSlide = (index: number) => {\n    setCurrentIndex(index);\n    setIsAutoPlaying(false);\n    setTimeout(() => setIsAutoPlaying(true), 10000); // Resume auto-play after 10 seconds\n  };\n\n  const goToPrevious = () => {\n    const newIndex = currentIndex === 0 ? testimonials.length - 1 : currentIndex - 1;\n    goToSlide(newIndex);\n  };\n\n  const goToNext = () => {\n    const newIndex = currentIndex === testimonials.length - 1 ? 0 : currentIndex + 1;\n    goToSlide(newIndex);\n  };\n\n  const currentTestimonial = testimonials[currentIndex];\n\n  if (loading) {\n    return (\n      <section id=\"testimonials\" className=\"py-20 bg-gradient-to-br from-portfolio-primary via-portfolio-dark to-portfolio-primary\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-4xl md:text-5xl font-heading font-bold text-white mb-4\">\n              What Clients Say\n            </h2>\n            <p className=\"text-lg text-white/80 max-w-2xl mx-auto\">\n              Feedback from collaborators and clients who trusted me with their vision.\n            </p>\n          </div>\n          <div className=\"max-w-4xl mx-auto\">\n            <Card className=\"bg-white/10 backdrop-blur-md border-white/20 text-white\">\n              <CardContent className=\"p-8 md:p-12 text-center\">\n                <div className=\"space-y-4\">\n                  <div className=\"w-20 h-20 bg-white/20 rounded-full mx-auto animate-pulse\"></div>\n                  <div className=\"h-6 bg-white/20 rounded animate-pulse mx-auto w-3/4\"></div>\n                  <div className=\"h-4 bg-white/20 rounded animate-pulse mx-auto w-1/2\"></div>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  if (testimonials.length === 0) {\n    return (\n      <section id=\"testimonials\" className=\"py-20 bg-gradient-to-br from-portfolio-primary via-portfolio-dark to-portfolio-primary\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-4xl md:text-5xl font-heading font-bold text-white mb-4\">\n              What Clients Say\n            </h2>\n            <p className=\"text-lg text-white/80 max-w-2xl mx-auto\">\n              Feedback from collaborators and clients who trusted me with their vision.\n            </p>\n          </div>\n          <div className=\"text-center py-12\">\n            <p className=\"text-lg text-white/80\">No testimonials available at the moment.</p>\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  return (\n    <section id=\"testimonials\" className=\"py-20 bg-gradient-to-br from-portfolio-primary via-portfolio-dark to-portfolio-primary\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-heading font-bold text-white mb-4\">\n            What Clients Say\n          </h2>\n          <p className=\"text-lg text-white/80 max-w-2xl mx-auto\">\n            Feedback from collaborators and clients who trusted me with their vision.\n          </p>\n        </div>\n\n        <div className=\"max-w-4xl mx-auto\">\n          <div className=\"relative\">\n            {/* Navigation Arrows */}\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              className=\"absolute -left-16 top-1/2 -translate-y-1/2 z-10 text-white hover:text-portfolio-accent hover:bg-white/10\"\n              onClick={goToPrevious}\n            >\n              <ChevronLeft size={24} />\n            </Button>\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              className=\"absolute -right-16 top-1/2 -translate-y-1/2 z-10 text-white hover:text-portfolio-accent hover:bg-white/10\"\n              onClick={goToNext}\n            >\n              <ChevronRight size={24} />\n            </Button>\n\n            {/* Testimonial Card */}\n            <Card className=\"bg-white/10 backdrop-blur-md border-white/20 text-white\">\n              <CardContent className=\"p-8 md:p-12 text-center\">\n                {/* Quote Icon */}\n                <div className=\"flex justify-center mb-6\">\n                  <Quote size={48} className=\"text-portfolio-accent opacity-50\" />\n                </div>\n\n                {/* Avatar */}\n                <div className=\"flex justify-center mb-6\">\n                  <div className=\"relative w-20 h-20 rounded-full overflow-hidden border-4 border-accent/50\">\n                    {currentTestimonial.avatar ? (\n                      <Image\n                        src={currentTestimonial.avatar}\n                        alt={currentTestimonial.name}\n                        fill\n                        className=\"object-cover\"\n                      />\n                    ) : (\n                      <div className=\"w-full h-full bg-accent flex items-center justify-center text-accent-foreground font-heading font-bold text-2xl\">\n                        {currentTestimonial.name.charAt(0)}\n                      </div>\n                    )}\n                  </div>\n                </div>\n\n                {/* Rating */}\n                {currentTestimonial.rating && (\n                  <div className=\"flex justify-center gap-1 mb-6\">\n                    {[...Array(5)].map((_, i) => (\n                      <Star\n                        key={i}\n                        size={20}\n                        className={`${i < currentTestimonial.rating!\n                          ? 'text-yellow-400 fill-current'\n                          : 'text-white/30'\n                          }`}\n                      />\n                    ))}\n                  </div>\n                )}\n\n                {/* Testimonial Content */}\n                <blockquote className=\"text-lg md:text-xl leading-relaxed mb-8 italic\">\n                  \"{currentTestimonial.content}\"\n                </blockquote>\n\n                {/* Author Info */}\n                <div>\n                  <cite className=\"text-portfolio-accent font-semibold text-lg not-italic\">\n                    {currentTestimonial.name}\n                  </cite>\n                  <div className=\"text-white/80 text-sm mt-1\">\n                    {currentTestimonial.role}\n                    {currentTestimonial.company && (\n                      <span> at {currentTestimonial.company}</span>\n                    )}\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* Dots Indicator */}\n          <div className=\"flex justify-center gap-3 mt-8\">\n            {testimonials.map((_, index) => (\n              <button\n                key={index}\n                onClick={() => goToSlide(index)}\n                className={`w-3 h-3 rounded-full transition-all duration-300 ${index === currentIndex\n                  ? 'bg-portfolio-accent scale-125'\n                  : 'bg-white/30 hover:bg-white/50'\n                  }`}\n                aria-label={`Go to testimonial ${index + 1}`}\n              />\n            ))}\n          </div>\n\n          {/* Stats */}\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8 mt-16 pt-16 border-t border-white/20\">\n            {[\n              { number: '98%', label: 'Client Satisfaction' },\n              { number: '50+', label: 'Projects Delivered' },\n              { number: '25+', label: 'Happy Clients' },\n              { number: '1M+', label: 'Views Generated' },\n            ].map((stat, index) => (\n              <div key={index} className=\"text-center\">\n                <div className=\"text-3xl md:text-4xl font-heading font-bold text-portfolio-accent mb-2\">\n                  {stat.number}\n                </div>\n                <div className=\"text-white/80 font-medium text-sm\">\n                  {stat.label}\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAPA;;;;;;;;AASe,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,oBAAoB;YACxB,IAAI;gBACF,MAAM,uBAAuB,MAAM,CAAA,GAAA,iHAAA,CAAA,0BAAuB,AAAD;gBACzD,gBAAgB;YAClB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gCAAgC;YAChD,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,iBAAiB,aAAa,MAAM,KAAK,GAAG;QAEjD,MAAM,WAAW,YAAY;YAC3B,gBAAgB,CAAC,YACf,cAAc,aAAa,MAAM,GAAG,IAAI,IAAI,YAAY;QAE5D,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAe,aAAa,MAAM;KAAC;IAEvC,MAAM,YAAY,CAAC;QACjB,gBAAgB;QAChB,iBAAiB;QACjB,WAAW,IAAM,iBAAiB,OAAO,QAAQ,oCAAoC;IACvF;IAEA,MAAM,eAAe;QACnB,MAAM,WAAW,iBAAiB,IAAI,aAAa,MAAM,GAAG,IAAI,eAAe;QAC/E,UAAU;IACZ;IAEA,MAAM,WAAW;QACf,MAAM,WAAW,iBAAiB,aAAa,MAAM,GAAG,IAAI,IAAI,eAAe;QAC/E,UAAU;IACZ;IAEA,MAAM,qBAAqB,YAAY,CAAC,aAAa;IAErD,IAAI,SAAS;QACX,qBACE,8OAAC;YAAQ,IAAG;YAAe,WAAU;sBACnC,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA8D;;;;;;0CAG5E,8OAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAIzD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQ/B;IAEA,IAAI,aAAa,MAAM,KAAK,GAAG;QAC7B,qBACE,8OAAC;YAAQ,IAAG;YAAe,WAAU;sBACnC,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA8D;;;;;;0CAG5E,8OAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAIzD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;IAK/C;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAe,WAAU;kBACnC,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA8D;;;;;;sCAG5E,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;8CAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;wCAAC,MAAM;;;;;;;;;;;8CAErB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;8CAET,cAAA,8OAAC,sNAAA,CAAA,eAAY;wCAAC,MAAM;;;;;;;;;;;8CAItB,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DAErB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;0DAI7B,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACZ,mBAAmB,MAAM,iBACxB,8OAAC,6HAAA,CAAA,UAAK;wDACJ,KAAK,mBAAmB,MAAM;wDAC9B,KAAK,mBAAmB,IAAI;wDAC5B,IAAI;wDACJ,WAAU;;;;;6EAGZ,8OAAC;wDAAI,WAAU;kEACZ,mBAAmB,IAAI,CAAC,MAAM,CAAC;;;;;;;;;;;;;;;;4CAOvC,mBAAmB,MAAM,kBACxB,8OAAC;gDAAI,WAAU;0DACZ;uDAAI,MAAM;iDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,kMAAA,CAAA,OAAI;wDAEH,MAAM;wDACN,WAAW,GAAG,IAAI,mBAAmB,MAAM,GACvC,iCACA,iBACA;uDALC;;;;;;;;;;0DAYb,8OAAC;gDAAW,WAAU;;oDAAiD;oDACnE,mBAAmB,OAAO;oDAAC;;;;;;;0DAI/B,8OAAC;;kEACC,8OAAC;wDAAK,WAAU;kEACb,mBAAmB,IAAI;;;;;;kEAE1B,8OAAC;wDAAI,WAAU;;4DACZ,mBAAmB,IAAI;4DACvB,mBAAmB,OAAO,kBACzB,8OAAC;;oEAAK;oEAAK,mBAAmB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASjD,8OAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAC,GAAG,sBACpB,8OAAC;oCAEC,SAAS,IAAM,UAAU;oCACzB,WAAW,CAAC,iDAAiD,EAAE,UAAU,eACrE,kCACA,iCACA;oCACJ,cAAY,CAAC,kBAAkB,EAAE,QAAQ,GAAG;mCANvC;;;;;;;;;;sCAYX,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,QAAQ;oCAAO,OAAO;gCAAsB;gCAC9C;oCAAE,QAAQ;oCAAO,OAAO;gCAAqB;gCAC7C;oCAAE,QAAQ;oCAAO,OAAO;gCAAgB;gCACxC;oCAAE,QAAQ;oCAAO,OAAO;gCAAkB;6BAC3C,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC;4CAAI,WAAU;sDACZ,KAAK,MAAM;;;;;;sDAEd,8OAAC;4CAAI,WAAU;sDACZ,KAAK,KAAK;;;;;;;mCALL;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcxB", "debugId": null}}, {"offset": {"line": 3157, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3183, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3208, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3236, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/Contact.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Send, Mail, Phone, MapPin, Linkedin, Instagram, Youtube, Facebook } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Label } from '@/components/ui/label';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\n\nexport default function Contact() {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    subject: '',\n    message: ''\n  });\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n    \n    // Simulate form submission\n    try {\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      setSubmitStatus('success');\n      setFormData({ name: '', email: '', subject: '', message: '' });\n    } catch (error) {\n      setSubmitStatus('error');\n    } finally {\n      setIsSubmitting(false);\n      setTimeout(() => setSubmitStatus('idle'), 5000);\n    }\n  };\n\n  const contactInfo = [\n    {\n      icon: Mail,\n      label: 'Email',\n      value: '<EMAIL>',\n      href: 'mailto:<EMAIL>'\n    },\n    {\n      icon: Phone,\n      label: 'Phone',\n      value: '+977 ************',\n      href: 'tel:+9779840692118'\n    },\n    {\n      icon: MapPin,\n      label: 'Location',\n      value: 'Kathmandu, Nepal',\n      href: '#'\n    }\n  ];\n\n  const socialLinks = [\n    { icon: Linkedin, href: 'https://www.linkedin.com/in/uttamrimal', label: 'LinkedIn' },\n    { icon: Instagram, href: 'https://www.instagram.com/uttamrimal', label: 'Instagram' },\n    { icon: Youtube, href: 'https://www.youtube.com/c/UttamRimal', label: 'YouTube' },\n    { icon: Facebook, href: 'https://www.facebook.com/uttamrimal', label: 'Facebook' },\n  ];\n\n  return (\n    <section id=\"contact\" className=\"py-20 bg-background\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-heading font-bold text-portfolio-primary mb-4\">\n            Let's Create Together\n          </h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n            Have a project in mind? Let's discuss how we can bring your vision to life through compelling video content.\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-6xl mx-auto\">\n          {/* Contact Form */}\n          <Card className=\"shadow-xl\">\n            <CardHeader>\n              <CardTitle className=\"text-2xl font-heading text-portfolio-primary\">\n                Send a Message\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <form onSubmit={handleSubmit} className=\"space-y-6\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"name\">Your Name *</Label>\n                    <Input\n                      id=\"name\"\n                      name=\"name\"\n                      type=\"text\"\n                      placeholder=\"e.g., Jane Doe\"\n                      value={formData.name}\n                      onChange={handleInputChange}\n                      required\n                      className=\"focus:ring-portfolio-primary focus:border-portfolio-primary\"\n                    />\n                  </div>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"email\">Your Email *</Label>\n                    <Input\n                      id=\"email\"\n                      name=\"email\"\n                      type=\"email\"\n                      placeholder=\"e.g., <EMAIL>\"\n                      value={formData.email}\n                      onChange={handleInputChange}\n                      required\n                      className=\"focus:ring-portfolio-primary focus:border-portfolio-primary\"\n                    />\n                  </div>\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"subject\">Subject</Label>\n                  <Input\n                    id=\"subject\"\n                    name=\"subject\"\n                    type=\"text\"\n                    placeholder=\"e.g., Video Editing Inquiry\"\n                    value={formData.subject}\n                    onChange={handleInputChange}\n                    className=\"focus:ring-portfolio-primary focus:border-portfolio-primary\"\n                  />\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"message\">Your Message *</Label>\n                  <Textarea\n                    id=\"message\"\n                    name=\"message\"\n                    placeholder=\"Tell me about your project...\"\n                    value={formData.message}\n                    onChange={handleInputChange}\n                    required\n                    rows={6}\n                    className=\"focus:ring-portfolio-primary focus:border-portfolio-primary\"\n                  />\n                </div>\n\n                <Button\n                  type=\"submit\"\n                  disabled={isSubmitting}\n                  className=\"w-full bg-portfolio-secondary hover:bg-portfolio-secondary/90 text-white py-3 rounded-full font-semibold transition-all duration-300 hover:scale-105 disabled:scale-100\"\n                >\n                  {isSubmitting ? (\n                    'Sending...'\n                  ) : (\n                    <>\n                      Send Message\n                      <Send size={18} className=\"ml-2\" />\n                    </>\n                  )}\n                </Button>\n\n                {/* Status Messages */}\n                {submitStatus === 'success' && (\n                  <div className=\"text-green-600 text-center font-medium\">\n                    Thank you! Your message has been sent successfully.\n                  </div>\n                )}\n                {submitStatus === 'error' && (\n                  <div className=\"text-red-600 text-center font-medium\">\n                    Sorry, there was an error sending your message. Please try again.\n                  </div>\n                )}\n              </form>\n            </CardContent>\n          </Card>\n\n          {/* Contact Information */}\n          <div className=\"space-y-8\">\n            <Card className=\"shadow-xl\">\n              <CardHeader>\n                <CardTitle className=\"text-2xl font-heading text-portfolio-primary\">\n                  Get in Touch\n                </CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-6\">\n                {contactInfo.map((info, index) => (\n                  <div key={index} className=\"flex items-center gap-4\">\n                    <div className=\"w-12 h-12 bg-portfolio-primary/10 rounded-full flex items-center justify-center\">\n                      <info.icon size={20} className=\"text-portfolio-primary\" />\n                    </div>\n                    <div>\n                      <div className=\"font-semibold text-portfolio-primary\">{info.label}</div>\n                      {info.href !== '#' ? (\n                        <a\n                          href={info.href}\n                          className=\"text-muted-foreground hover:text-portfolio-secondary transition-colors\"\n                        >\n                          {info.value}\n                        </a>\n                      ) : (\n                        <div className=\"text-muted-foreground\">{info.value}</div>\n                      )}\n                    </div>\n                  </div>\n                ))}\n              </CardContent>\n            </Card>\n\n            {/* Social Links */}\n            <Card className=\"shadow-xl\">\n              <CardHeader>\n                <CardTitle className=\"text-2xl font-heading text-portfolio-primary\">\n                  Follow Me\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"flex gap-4\">\n                  {socialLinks.map(({ icon: Icon, href, label }) => (\n                    <a\n                      key={label}\n                      href={href}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"w-12 h-12 bg-portfolio-primary/10 hover:bg-portfolio-accent text-portfolio-primary hover:text-white rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110\"\n                      aria-label={label}\n                    >\n                      <Icon size={20} />\n                    </a>\n                  ))}\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* WhatsApp Quick Contact */}\n            <Card className=\"shadow-xl bg-green-50 border-green-200\">\n              <CardContent className=\"p-6\">\n                <div className=\"text-center\">\n                  <h3 className=\"font-heading font-semibold text-green-800 mb-2\">\n                    Quick Chat on WhatsApp\n                  </h3>\n                  <p className=\"text-green-600 text-sm mb-4\">\n                    Need immediate assistance? Let's chat!\n                  </p>\n                  <a\n                    href=\"https://wa.me/9779840692118\"\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"inline-flex items-center gap-2 bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-full font-semibold transition-all duration-300 hover:scale-105\"\n                  >\n                    <Phone size={18} />\n                    Chat on WhatsApp\n                  </a>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAUe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,SAAS;QACT,SAAS;IACX;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgC;IAE/E,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAEhB,2BAA2B;QAC3B,IAAI;YACF,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,gBAAgB;YAChB,YAAY;gBAAE,MAAM;gBAAI,OAAO;gBAAI,SAAS;gBAAI,SAAS;YAAG;QAC9D,EAAE,OAAO,OAAO;YACd,gBAAgB;QAClB,SAAU;YACR,gBAAgB;YAChB,WAAW,IAAM,gBAAgB,SAAS;QAC5C;IACF;IAEA,MAAM,cAAc;QAClB;YACE,MAAM,kMAAA,CAAA,OAAI;YACV,OAAO;YACP,OAAO;YACP,MAAM;QACR;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,OAAO;YACP,MAAM;QACR;QACA;YACE,MAAM,0MAAA,CAAA,SAAM;YACZ,OAAO;YACP,OAAO;YACP,MAAM;QACR;KACD;IAED,MAAM,cAAc;QAClB;YAAE,MAAM,0MAAA,CAAA,WAAQ;YAAE,MAAM;YAA0C,OAAO;QAAW;QACpF;YAAE,MAAM,4MAAA,CAAA,YAAS;YAAE,MAAM;YAAwC,OAAO;QAAY;QACpF;YAAE,MAAM,wMAAA,CAAA,UAAO;YAAE,MAAM;YAAwC,OAAO;QAAU;QAChF;YAAE,MAAM,0MAAA,CAAA,WAAQ;YAAE,MAAM;YAAuC,OAAO;QAAW;KAClF;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAU,WAAU;kBAC9B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0E;;;;;;sCAGxF,8OAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;8BAKjE,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAA+C;;;;;;;;;;;8CAItE,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAK,UAAU;wCAAc,WAAU;;0DACtC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAO;;;;;;0EACtB,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAK;gEACL,MAAK;gEACL,aAAY;gEACZ,OAAO,SAAS,IAAI;gEACpB,UAAU;gEACV,QAAQ;gEACR,WAAU;;;;;;;;;;;;kEAGd,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAQ;;;;;;0EACvB,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAK;gEACL,MAAK;gEACL,aAAY;gEACZ,OAAO,SAAS,KAAK;gEACrB,UAAU;gEACV,QAAQ;gEACR,WAAU;;;;;;;;;;;;;;;;;;0DAKhB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAU;;;;;;kEACzB,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,MAAK;wDACL,aAAY;wDACZ,OAAO,SAAS,OAAO;wDACvB,UAAU;wDACV,WAAU;;;;;;;;;;;;0DAId,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAU;;;;;;kEACzB,8OAAC,oIAAA,CAAA,WAAQ;wDACP,IAAG;wDACH,MAAK;wDACL,aAAY;wDACZ,OAAO,SAAS,OAAO;wDACvB,UAAU;wDACV,QAAQ;wDACR,MAAM;wDACN,WAAU;;;;;;;;;;;;0DAId,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,UAAU;gDACV,WAAU;0DAET,eACC,6BAEA;;wDAAE;sEAEA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,MAAM;4DAAI,WAAU;;;;;;;;;;;;;4CAM/B,iBAAiB,2BAChB,8OAAC;gDAAI,WAAU;0DAAyC;;;;;;4CAIzD,iBAAiB,yBAChB,8OAAC;gDAAI,WAAU;0DAAuC;;;;;;;;;;;;;;;;;;;;;;;sCAS9D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAA+C;;;;;;;;;;;sDAItE,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;sDACpB,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC;oDAAgB,WAAU;;sEACzB,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,KAAK,IAAI;gEAAC,MAAM;gEAAI,WAAU;;;;;;;;;;;sEAEjC,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;8EAAwC,KAAK,KAAK;;;;;;gEAChE,KAAK,IAAI,KAAK,oBACb,8OAAC;oEACC,MAAM,KAAK,IAAI;oEACf,WAAU;8EAET,KAAK,KAAK;;;;;yFAGb,8OAAC;oEAAI,WAAU;8EAAyB,KAAK,KAAK;;;;;;;;;;;;;mDAd9C;;;;;;;;;;;;;;;;8CAuBhB,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAA+C;;;;;;;;;;;sDAItE,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAI,WAAU;0DACZ,YAAY,GAAG,CAAC,CAAC,EAAE,MAAM,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,iBAC3C,8OAAC;wDAEC,MAAM;wDACN,QAAO;wDACP,KAAI;wDACJ,WAAU;wDACV,cAAY;kEAEZ,cAAA,8OAAC;4DAAK,MAAM;;;;;;uDAPP;;;;;;;;;;;;;;;;;;;;;8CAef,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAiD;;;;;;8DAG/D,8OAAC;oDAAE,WAAU;8DAA8B;;;;;;8DAG3C,8OAAC;oDACC,MAAK;oDACL,QAAO;oDACP,KAAI;oDACJ,WAAU;;sEAEV,8OAAC,oMAAA,CAAA,QAAK;4DAAC,MAAM;;;;;;wDAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWvC", "debugId": null}}, {"offset": {"line": 3816, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/ScrollToTop.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { ArrowUp } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\n\nexport default function ScrollToTop() {\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    const toggleVisibility = () => {\n      if (window.pageYOffset > 300) {\n        setIsVisible(true);\n      } else {\n        setIsVisible(false);\n      }\n    };\n\n    window.addEventListener('scroll', toggleVisibility);\n    return () => window.removeEventListener('scroll', toggleVisibility);\n  }, []);\n\n  const scrollToTop = () => {\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth',\n    });\n  };\n\n  if (!isVisible) {\n    return null;\n  }\n\n  return (\n    <Button\n      onClick={scrollToTop}\n      className=\"fixed bottom-8 right-8 z-50 w-12 h-12 bg-accent hover:bg-accent/90 text-accent-foreground rounded-full shadow-lg hover:scale-110 transition-all duration-300\"\n      aria-label=\"Scroll to top\"\n    >\n      <ArrowUp size={20} />\n    </Button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,IAAI,OAAO,WAAW,GAAG,KAAK;gBAC5B,aAAa;YACf,OAAO;gBACL,aAAa;YACf;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC;YACd,KAAK;YACL,UAAU;QACZ;IACF;IAEA,IAAI,CAAC,WAAW;QACd,OAAO;IACT;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,SAAS;QACT,WAAU;QACV,cAAW;kBAEX,cAAA,8OAAC,4MAAA,CAAA,UAAO;YAAC,MAAM;;;;;;;;;;;AAGrB", "debugId": null}}]}