{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/cms/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI!;\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n\ninterface MongooseCache {\n  conn: typeof mongoose | null;\n  promise: Promise<typeof mongoose> | null;\n}\n\ndeclare global {\n  var mongoose: MongooseCache | undefined;\n}\n\nlet cached: MongooseCache = global.mongoose || { conn: null, promise: null };\n\nif (!global.mongoose) {\n  global.mongoose = cached;\n}\n\nasync function connectDB(): Promise<typeof mongoose> {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      console.log('✅ Connected to MongoDB');\n      return mongoose;\n    });\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MAAM;AAClB;AAWA,IAAI,SAAwB,OAAO,QAAQ,IAAI;IAAE,MAAM;IAAM,SAAS;AAAK;AAE3E,IAAI,CAAC,OAAO,QAAQ,EAAE;IACpB,OAAO,QAAQ,GAAG;AACpB;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/cms/src/models/User.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\n\nexport interface IUser extends Document {\n  email: string;\n  password: string;\n  name: string;\n  role: 'admin' | 'editor';\n  isActive: boolean;\n  lastLogin?: Date;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nconst UserSchema = new Schema<IUser>({\n  email: {\n    type: String,\n    required: true,\n    unique: true,\n    lowercase: true,\n    trim: true,\n  },\n  password: {\n    type: String,\n    required: true,\n    minlength: 6,\n  },\n  name: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n  role: {\n    type: String,\n    enum: ['admin', 'editor'],\n    default: 'editor',\n  },\n  isActive: {\n    type: Boolean,\n    default: true,\n  },\n  lastLogin: {\n    type: Date,\n  },\n}, {\n  timestamps: true,\n});\n\n// Index for faster queries\nUserSchema.index({ email: 1 });\nUserSchema.index({ role: 1 });\n\nexport default mongoose.models.User || mongoose.model<IUser>('User', UserSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAaA,MAAM,aAAa,IAAI,yGAAA,CAAA,SAAM,CAAQ;IACnC,OAAO;QACL,MAAM;QACN,UAAU;QACV,QAAQ;QACR,WAAW;QACX,MAAM;IACR;IACA,UAAU;QACR,MAAM;QACN,UAAU;QACV,WAAW;IACb;IACA,MAAM;QACJ,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,MAAM;QACJ,MAAM;QACN,MAAM;YAAC;YAAS;SAAS;QACzB,SAAS;IACX;IACA,UAAU;QACR,MAAM;QACN,SAAS;IACX;IACA,WAAW;QACT,MAAM;IACR;AACF,GAAG;IACD,YAAY;AACd;AAEA,2BAA2B;AAC3B,WAAW,KAAK,CAAC;IAAE,OAAO;AAAE;AAC5B,WAAW,KAAK,CAAC;IAAE,MAAM;AAAE;uCAEZ,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAQ,QAAQ", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/cms/src/app/api/status/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\";\nimport connectDB from \"@/lib/mongodb\";\nimport User from \"@/models/User\";\n\nexport async function GET() {\n  try {\n    // Test database connection\n    await connectDB();\n\n    // Check if admin user exists\n    const adminExists = await User.findOne({ role: \"admin\" });\n\n    return NextResponse.json({\n      success: true,\n      status: {\n        database: \"connected\",\n        adminUser: adminExists ? \"exists\" : \"missing\",\n        environment: process.env.NODE_ENV || \"development\",\n        mongoUri: process.env.MONGODB_URI ? \"configured\" : \"missing\",\n      },\n    });\n  } catch (error) {\n    return NextResponse.json({\n      success: false,\n      status: {\n        database: \"disconnected\",\n        adminUser: \"unknown\",\n        environment: process.env.NODE_ENV || \"development\",\n        mongoUri: process.env.MONGODB_URI ? \"configured\" : \"missing\",\n      },\n      error: error instanceof Error ? error.message : \"Unknown error\",\n    });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,eAAe;IACpB,IAAI;QACF,2BAA2B;QAC3B,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,6BAA6B;QAC7B,MAAM,cAAc,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;YAAE,MAAM;QAAQ;QAEvD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,QAAQ;gBACN,UAAU;gBACV,WAAW,cAAc,WAAW;gBACpC,aAAa,mDAAwB;gBACrC,UAAU,QAAQ,GAAG,CAAC,WAAW,GAAG,eAAe;YACrD;QACF;IACF,EAAE,OAAO,OAAO;QACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,QAAQ;gBACN,UAAU;gBACV,WAAW;gBACX,aAAa,mDAAwB;gBACrC,UAAU,QAAQ,GAAG,CAAC,WAAW,GAAG,eAAe;YACrD;YACA,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF", "debugId": null}}]}