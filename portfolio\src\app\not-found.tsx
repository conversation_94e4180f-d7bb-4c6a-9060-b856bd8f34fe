import Link from 'next/link';
import { Home, ArrowLeft, Search, FileX } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Footer from '@/components/Footer';

export default function NotFound() {
  return (
    <main>
      
      <div className="min-h-screen bg-gradient-to-br from-muted via-background to-muted flex items-center justify-center px-4">
        <div className="max-w-2xl mx-auto text-center">
          {/* 404 Animation */}
          <div className="relative mb-8">
            <div className="text-8xl md:text-9xl font-bold text-primary/20 select-none">
              404
            </div>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="bg-card p-6 rounded-full shadow-lg animate-bounce">
                <FileX size={48} className="text-primary" />
              </div>
            </div>
          </div>

          {/* Error Message */}
          <div className="mb-8">
            <h1 className="text-3xl md:text-4xl font-heading font-bold text-primary mb-4">
              Oops! Page Not Found
            </h1>
            <p className="text-lg text-muted-foreground leading-relaxed mb-6">
              The page you're looking for seems to have wandered off into the digital void. 
              Don't worry, even the best video editors sometimes lose track of their clips!
            </p>
            <p className="text-muted-foreground">
              Let's get you back on track with some helpful options below.
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Button asChild>
              <Link href="/" className="inline-flex items-center gap-2">
                <Home size={20} />
                Back to Home
              </Link>
            </Button>
            
            <Button variant="outline" asChild>
              <Link href="/blog" className="inline-flex items-center gap-2">
                <Search size={20} />
                Browse Blog
              </Link>
            </Button>
            
            <Button variant="outline" asChild>
              <Link href="/videos" className="inline-flex items-center gap-2">
                <ArrowLeft size={20} />
                View Portfolio
              </Link>
            </Button>
          </div>

          {/* Quick Links */}
          <div className="bg-card rounded-xl shadow-md p-6 mb-8">
            <h2 className="text-xl font-semibold text-primary mb-4">
              Popular Pages
            </h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Link
                href="/videos"
                className="p-3 rounded-lg hover:bg-muted transition-colors text-center group"
              >
                <div className="text-accent group-hover:scale-110 transition-transform duration-300 mb-2">
                  🎬
                </div>
                <span className="text-sm font-medium">Videos</span>
              </Link>

              <Link
                href="/reels"
                className="p-3 rounded-lg hover:bg-muted transition-colors text-center group"
              >
                <div className="text-accent group-hover:scale-110 transition-transform duration-300 mb-2">
                  📱
                </div>
                <span className="text-sm font-medium">Reels</span>
              </Link>

              <Link
                href="/blog"
                className="p-3 rounded-lg hover:bg-muted transition-colors text-center group"
              >
                <div className="text-accent group-hover:scale-110 transition-transform duration-300 mb-2">
                  📝
                </div>
                <span className="text-sm font-medium">Blog</span>
              </Link>

              <Link
                href="/clients"
                className="p-3 rounded-lg hover:bg-muted transition-colors text-center group"
              >
                <div className="text-accent group-hover:scale-110 transition-transform duration-300 mb-2">
                  🤝
                </div>
                <span className="text-sm font-medium">Clients</span>
              </Link>
            </div>
          </div>

          {/* Fun Message */}
          <div className="text-sm text-muted-foreground">
            <p>
              💡 <strong>Pro Tip:</strong> Just like in video editing, sometimes the best content 
              is found when you explore different angles. Try the links above!
            </p>
          </div>
        </div>
      </div>

      <Footer />
    </main>
  );
}
