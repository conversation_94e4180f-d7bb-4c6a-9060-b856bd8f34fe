{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/api/:path*{(\\\\.json)}?", "originalSource": "/api/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "z2WXuwZtdGk/LJ62+5rY6tUPjYA9705wjsFO5fRpOeU=", "__NEXT_PREVIEW_MODE_ID": "da65f2025f1f00531825062edc11fdc0", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "3a1b99047d31475550e301f577edeaecdb223dde8c23c6d6af3db13c32b62e60", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "fe714814174e39d6a8d0b8a79d62994e9a4574a85e3e3f968a75409c0499eb5f"}}}, "instrumentation": null, "functions": {}}