{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/cms/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI!;\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n\ninterface MongooseCache {\n  conn: typeof mongoose | null;\n  promise: Promise<typeof mongoose> | null;\n}\n\ndeclare global {\n  var mongoose: MongooseCache | undefined;\n}\n\nlet cached: MongooseCache = global.mongoose || { conn: null, promise: null };\n\nif (!global.mongoose) {\n  global.mongoose = cached;\n}\n\nasync function connectDB(): Promise<typeof mongoose> {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      console.log('✅ Connected to MongoDB');\n      return mongoose;\n    });\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MAAM;AAClB;AAWA,IAAI,SAAwB,OAAO,QAAQ,IAAI;IAAE,MAAM;IAAM,SAAS;AAAK;AAE3E,IAAI,CAAC,OAAO,QAAQ,EAAE;IACpB,OAAO,QAAQ,GAAG;AACpB;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/cms/src/models/User.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\n\nexport interface IUser extends Document {\n  email: string;\n  password: string;\n  name: string;\n  role: 'admin' | 'editor';\n  isActive: boolean;\n  lastLogin?: Date;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nconst UserSchema = new Schema<IUser>({\n  email: {\n    type: String,\n    required: true,\n    unique: true,\n    lowercase: true,\n    trim: true,\n  },\n  password: {\n    type: String,\n    required: true,\n    minlength: 6,\n  },\n  name: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n  role: {\n    type: String,\n    enum: ['admin', 'editor'],\n    default: 'editor',\n  },\n  isActive: {\n    type: Boolean,\n    default: true,\n  },\n  lastLogin: {\n    type: Date,\n  },\n}, {\n  timestamps: true,\n});\n\n// Index for faster queries\nUserSchema.index({ email: 1 });\nUserSchema.index({ role: 1 });\n\nexport default mongoose.models.User || mongoose.model<IUser>('User', UserSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAaA,MAAM,aAAa,IAAI,yGAAA,CAAA,SAAM,CAAQ;IACnC,OAAO;QACL,MAAM;QACN,UAAU;QACV,QAAQ;QACR,WAAW;QACX,MAAM;IACR;IACA,UAAU;QACR,MAAM;QACN,UAAU;QACV,WAAW;IACb;IACA,MAAM;QACJ,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,MAAM;QACJ,MAAM;QACN,MAAM;YAAC;YAAS;SAAS;QACzB,SAAS;IACX;IACA,UAAU;QACR,MAAM;QACN,SAAS;IACX;IACA,WAAW;QACT,MAAM;IACR;AACF,GAAG;IACD,YAAY;AACd;AAEA,2BAA2B;AAC3B,WAAW,KAAK,CAAC;IAAE,OAAO;AAAE;AAC5B,WAAW,KAAK,CAAC;IAAE,MAAM;AAAE;uCAEZ,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAQ,QAAQ", "debugId": null}}, {"offset": {"line": 199, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/cms/src/lib/auth.ts"], "sourcesContent": ["import jwt from 'jsonwebtoken';\nimport bcrypt from 'bcryptjs';\nimport { NextRequest } from 'next/server';\n\nconst JWT_SECRET = process.env.JWT_SECRET!;\n\nif (!JWT_SECRET) {\n  throw new Error('Please define the JWT_SECRET environment variable');\n}\n\nexport interface JWTPayload {\n  userId: string;\n  email: string;\n  role: string;\n  iat?: number;\n  exp?: number;\n}\n\nexport const hashPassword = async (password: string): Promise<string> => {\n  const saltRounds = 12;\n  return await bcrypt.hash(password, saltRounds);\n};\n\nexport const comparePassword = async (\n  password: string,\n  hashedPassword: string\n): Promise<boolean> => {\n  return await bcrypt.compare(password, hashedPassword);\n};\n\nexport const generateToken = (payload: Omit<JWTPayload, 'iat' | 'exp'>): string => {\n  return jwt.sign(payload, JWT_SECRET, {\n    expiresIn: '7d', // Token expires in 7 days\n  });\n};\n\nexport const verifyToken = (token: string): JWTPayload | null => {\n  try {\n    return jwt.verify(token, JWT_SECRET) as JWTPayload;\n  } catch (error) {\n    return null;\n  }\n};\n\nexport const getTokenFromRequest = (request: NextRequest): string | null => {\n  // Check Authorization header\n  const authHeader = request.headers.get('authorization');\n  if (authHeader && authHeader.startsWith('Bearer ')) {\n    return authHeader.substring(7);\n  }\n\n  // Check cookies\n  const token = request.cookies.get('auth-token')?.value;\n  return token || null;\n};\n\nexport const getUserFromRequest = async (request: NextRequest): Promise<JWTPayload | null> => {\n  const token = getTokenFromRequest(request);\n  if (!token) {\n    return null;\n  }\n\n  return verifyToken(token);\n};\n\n// Middleware helper for protected routes\nexport const requireAuth = (handler: Function) => {\n  return async (request: NextRequest, context: any) => {\n    const user = await getUserFromRequest(request);\n    \n    if (!user) {\n      return new Response(\n        JSON.stringify({ error: 'Authentication required' }),\n        {\n          status: 401,\n          headers: { 'Content-Type': 'application/json' },\n        }\n      );\n    }\n\n    // Add user to request context\n    (request as any).user = user;\n    return handler(request, context);\n  };\n};\n\n// Admin-only middleware\nexport const requireAdmin = (handler: Function) => {\n  return async (request: NextRequest, context: any) => {\n    const user = await getUserFromRequest(request);\n    \n    if (!user) {\n      return new Response(\n        JSON.stringify({ error: 'Authentication required' }),\n        {\n          status: 401,\n          headers: { 'Content-Type': 'application/json' },\n        }\n      );\n    }\n\n    if (user.role !== 'admin') {\n      return new Response(\n        JSON.stringify({ error: 'Admin access required' }),\n        {\n          status: 403,\n          headers: { 'Content-Type': 'application/json' },\n        }\n      );\n    }\n\n    (request as any).user = user;\n    return handler(request, context);\n  };\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAGA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU;AAEzC,IAAI,CAAC,YAAY;IACf,MAAM,IAAI,MAAM;AAClB;AAUO,MAAM,eAAe,OAAO;IACjC,MAAM,aAAa;IACnB,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AACrC;AAEO,MAAM,kBAAkB,OAC7B,UACA;IAEA,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AACxC;AAEO,MAAM,gBAAgB,CAAC;IAC5B,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QACnC,WAAW;IACb;AACF;AAEO,MAAM,cAAc,CAAC;IAC1B,IAAI;QACF,OAAO,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;IAC3B,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAEO,MAAM,sBAAsB,CAAC;IAClC,6BAA6B;IAC7B,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IACvC,IAAI,cAAc,WAAW,UAAU,CAAC,YAAY;QAClD,OAAO,WAAW,SAAS,CAAC;IAC9B;IAEA,gBAAgB;IAChB,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;IACjD,OAAO,SAAS;AAClB;AAEO,MAAM,qBAAqB,OAAO;IACvC,MAAM,QAAQ,oBAAoB;IAClC,IAAI,CAAC,OAAO;QACV,OAAO;IACT;IAEA,OAAO,YAAY;AACrB;AAGO,MAAM,cAAc,CAAC;IAC1B,OAAO,OAAO,SAAsB;QAClC,MAAM,OAAO,MAAM,mBAAmB;QAEtC,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,OAAO;YAA0B,IAClD;gBACE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QAEJ;QAEA,8BAA8B;QAC7B,QAAgB,IAAI,GAAG;QACxB,OAAO,QAAQ,SAAS;IAC1B;AACF;AAGO,MAAM,eAAe,CAAC;IAC3B,OAAO,OAAO,SAAsB;QAClC,MAAM,OAAO,MAAM,mBAAmB;QAEtC,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,OAAO;YAA0B,IAClD;gBACE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QAEJ;QAEA,IAAI,KAAK,IAAI,KAAK,SAAS;YACzB,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,OAAO;YAAwB,IAChD;gBACE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QAEJ;QAEC,QAAgB,IAAI,GAAG;QACxB,OAAO,QAAQ,SAAS;IAC1B;AACF", "debugId": null}}, {"offset": {"line": 304, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/cms/src/app/api/init/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\";\nimport connectDB from \"@/lib/mongodb\";\nimport User from \"@/models/User\";\nimport { hashPassword } from \"@/lib/auth\";\n\nexport async function POST() {\n  try {\n    await connectDB();\n\n    const adminEmail = process.env.ADMIN_EMAIL || \"<EMAIL>\";\n    const adminPassword = process.env.ADMIN_PASSWORD || \"admin123\";\n\n    // Check if admin already exists\n    const existingAdmin = await User.findOne({ email: adminEmail });\n\n    if (existingAdmin) {\n      return NextResponse.json({\n        success: true,\n        message: \"Admin user already exists\",\n        credentials: {\n          email: adminEmail,\n          password: adminPassword,\n        },\n      });\n    }\n\n    // Create admin user\n    const hashedPassword = await hashPassword(adminPassword);\n\n    const adminUser = new User({\n      email: adminEmail,\n      password: hashedPassword,\n      name: \"Admin User\",\n      role: \"admin\",\n      isActive: true,\n    });\n\n    await adminUser.save();\n\n    return NextResponse.json({\n      success: true,\n      message: \"Admin user created successfully\",\n      credentials: {\n        email: adminEmail,\n        password: adminPassword,\n      },\n    });\n  } catch (error) {\n    console.error(\"Error initializing admin user:\", error);\n    return NextResponse.json(\n      { error: \"Failed to initialize admin user\" },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,aAAa,QAAQ,GAAG,CAAC,WAAW,IAAI;QAC9C,MAAM,gBAAgB,QAAQ,GAAG,CAAC,cAAc,IAAI;QAEpD,gCAAgC;QAChC,MAAM,gBAAgB,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;YAAE,OAAO;QAAW;QAE7D,IAAI,eAAe;YACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;gBACT,aAAa;oBACX,OAAO;oBACP,UAAU;gBACZ;YACF;QACF;QAEA,oBAAoB;QACpB,MAAM,iBAAiB,MAAM,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD,EAAE;QAE1C,MAAM,YAAY,IAAI,uHAAA,CAAA,UAAI,CAAC;YACzB,OAAO;YACP,UAAU;YACV,MAAM;YACN,MAAM;YACN,UAAU;QACZ;QAEA,MAAM,UAAU,IAAI;QAEpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,aAAa;gBACX,OAAO;gBACP,UAAU;YACZ;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAkC,GAC3C;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}