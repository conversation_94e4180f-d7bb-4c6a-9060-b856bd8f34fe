import { Building, Globe, Eye, Heart } from 'lucide-react';

interface StatItem {
  number: string;
  label: string;
  icon: React.ReactNode;
  color: string;
}

interface ClientsStatsProps {
  className?: string;
}

export default function ClientsStats({ className = '' }: ClientsStatsProps) {
  const stats: StatItem[] = [
    {
      number: '50+',
      label: 'Projects Completed',
      icon: <Building size={24} />,
      color: 'text-blue-600'
    },
    {
      number: '25+',
      label: 'Happy Clients',
      icon: <Heart size={24} />,
      color: 'text-red-500'
    },
    {
      number: '1M+',
      label: 'Views Generated',
      icon: <Eye size={24} />,
      color: 'text-green-600'
    },
    {
      number: '98%',
      label: 'Client Satisfaction',
      icon: <Globe size={24} />,
      color: 'text-purple-600'
    },
  ];

  return (
    <div className={`grid grid-cols-2 md:grid-cols-4 gap-6 ${className}`}>
      {stats.map((stat, index) => (
        <div
          key={index}
          className="bg-card p-6 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 text-center group hover:scale-105"
        >
          <div className={`flex justify-center mb-4 ${stat.color} group-hover:scale-110 transition-transform duration-300`}>
            {stat.icon}
          </div>
          <div className="text-3xl md:text-4xl font-heading font-bold text-accent mb-2 group-hover:text-primary transition-colors duration-300">
            {stat.number}
          </div>
          <div className="text-muted-foreground font-medium text-sm md:text-base">
            {stat.label}
          </div>
        </div>
      ))}
    </div>
  );
}
