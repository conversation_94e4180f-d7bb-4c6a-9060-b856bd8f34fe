{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/cms/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI!;\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n\ninterface MongooseCache {\n  conn: typeof mongoose | null;\n  promise: Promise<typeof mongoose> | null;\n}\n\ndeclare global {\n  var mongoose: MongooseCache | undefined;\n}\n\nlet cached: MongooseCache = global.mongoose || { conn: null, promise: null };\n\nif (!global.mongoose) {\n  global.mongoose = cached;\n}\n\nasync function connectDB(): Promise<typeof mongoose> {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      console.log('✅ Connected to MongoDB');\n      return mongoose;\n    });\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MAAM;AAClB;AAWA,IAAI,SAAwB,OAAO,QAAQ,IAAI;IAAE,MAAM;IAAM,SAAS;AAAK;AAE3E,IAAI,CAAC,OAAO,QAAQ,EAAE;IACpB,OAAO,QAAQ,GAAG;AACpB;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/cms/src/models/BlogPost.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\n\nexport interface IBlogPost extends Document {\n  title: string;\n  slug: string;\n  excerpt: string;\n  content: string;\n  author: string;\n  publishedAt?: Date;\n  updatedAt: Date;\n  thumbnail?: string;\n  tags: string[];\n  category: string;\n  readTime: number;\n  featured: boolean;\n  status: 'draft' | 'published' | 'archived';\n  seoTitle?: string;\n  seoDescription?: string;\n  seoKeywords?: string[];\n  views: number;\n}\n\nconst BlogPostSchema = new Schema<IBlogPost>({\n  title: {\n    type: String,\n    required: true,\n    trim: true,\n    maxlength: 200,\n  },\n  slug: {\n    type: String,\n    required: true,\n    unique: true,\n    lowercase: true,\n    trim: true,\n  },\n  excerpt: {\n    type: String,\n    required: true,\n    maxlength: 500,\n  },\n  content: {\n    type: String,\n    required: true,\n  },\n  author: {\n    type: String,\n    required: true,\n    default: 'Uttam Rimal',\n  },\n  publishedAt: {\n    type: Date,\n  },\n  thumbnail: {\n    type: String,\n  },\n  tags: [{\n    type: String,\n    trim: true,\n    lowercase: true,\n  }],\n  category: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n  readTime: {\n    type: Number,\n    default: 5,\n  },\n  featured: {\n    type: Boolean,\n    default: false,\n  },\n  status: {\n    type: String,\n    enum: ['draft', 'published', 'archived'],\n    default: 'draft',\n  },\n  seoTitle: {\n    type: String,\n    maxlength: 60,\n  },\n  seoDescription: {\n    type: String,\n    maxlength: 160,\n  },\n  seoKeywords: [{\n    type: String,\n    trim: true,\n    lowercase: true,\n  }],\n  views: {\n    type: Number,\n    default: 0,\n  },\n}, {\n  timestamps: true,\n});\n\n// Indexes for better performance\nBlogPostSchema.index({ slug: 1 });\nBlogPostSchema.index({ status: 1, publishedAt: -1 });\nBlogPostSchema.index({ category: 1 });\nBlogPostSchema.index({ tags: 1 });\nBlogPostSchema.index({ featured: 1 });\n\n// Pre-save middleware to auto-generate slug if not provided\nBlogPostSchema.pre('save', function(next) {\n  if (!this.slug && this.title) {\n    const slugify = require('slugify');\n    this.slug = slugify(this.title, {\n      lower: true,\n      strict: true,\n      remove: /[*+~.()'\"!:@]/g,\n    });\n  }\n  \n  // Auto-set publishedAt when status changes to published\n  if (this.status === 'published' && !this.publishedAt) {\n    this.publishedAt = new Date();\n  }\n  \n  next();\n});\n\nexport default mongoose.models.BlogPost || mongoose.model<IBlogPost>('BlogPost', BlogPostSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAsBA,MAAM,iBAAiB,IAAI,yGAAA,CAAA,SAAM,CAAY;IAC3C,OAAO;QACL,MAAM;QACN,UAAU;QACV,MAAM;QACN,WAAW;IACb;IACA,MAAM;QACJ,MAAM;QACN,UAAU;QACV,QAAQ;QACR,WAAW;QACX,MAAM;IACR;IACA,SAAS;QACP,MAAM;QACN,UAAU;QACV,WAAW;IACb;IACA,SAAS;QACP,MAAM;QACN,UAAU;IACZ;IACA,QAAQ;QACN,MAAM;QACN,UAAU;QACV,SAAS;IACX;IACA,aAAa;QACX,MAAM;IACR;IACA,WAAW;QACT,MAAM;IACR;IACA,MAAM;QAAC;YACL,MAAM;YACN,MAAM;YACN,WAAW;QACb;KAAE;IACF,UAAU;QACR,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,UAAU;QACR,MAAM;QACN,SAAS;IACX;IACA,UAAU;QACR,MAAM;QACN,SAAS;IACX;IACA,QAAQ;QACN,MAAM;QACN,MAAM;YAAC;YAAS;YAAa;SAAW;QACxC,SAAS;IACX;IACA,UAAU;QACR,MAAM;QACN,WAAW;IACb;IACA,gBAAgB;QACd,MAAM;QACN,WAAW;IACb;IACA,aAAa;QAAC;YACZ,MAAM;YACN,MAAM;YACN,WAAW;QACb;KAAE;IACF,OAAO;QACL,MAAM;QACN,SAAS;IACX;AACF,GAAG;IACD,YAAY;AACd;AAEA,iCAAiC;AACjC,eAAe,KAAK,CAAC;IAAE,MAAM;AAAE;AAC/B,eAAe,KAAK,CAAC;IAAE,QAAQ;IAAG,aAAa,CAAC;AAAE;AAClD,eAAe,KAAK,CAAC;IAAE,UAAU;AAAE;AACnC,eAAe,KAAK,CAAC;IAAE,MAAM;AAAE;AAC/B,eAAe,KAAK,CAAC;IAAE,UAAU;AAAE;AAEnC,4DAA4D;AAC5D,eAAe,GAAG,CAAC,QAAQ,SAAS,IAAI;IACtC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;QAC5B,MAAM;QACN,IAAI,CAAC,IAAI,GAAG,QAAQ,IAAI,CAAC,KAAK,EAAE;YAC9B,OAAO;YACP,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,wDAAwD;IACxD,IAAI,IAAI,CAAC,MAAM,KAAK,eAAe,CAAC,IAAI,CAAC,WAAW,EAAE;QACpD,IAAI,CAAC,WAAW,GAAG,IAAI;IACzB;IAEA;AACF;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,QAAQ,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAY,YAAY", "debugId": null}}, {"offset": {"line": 274, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/cms/src/lib/auth.ts"], "sourcesContent": ["import jwt from 'jsonwebtoken';\nimport bcrypt from 'bcryptjs';\nimport { NextRequest } from 'next/server';\n\nconst JWT_SECRET = process.env.JWT_SECRET!;\n\nif (!JWT_SECRET) {\n  throw new Error('Please define the JWT_SECRET environment variable');\n}\n\nexport interface JWTPayload {\n  userId: string;\n  email: string;\n  role: string;\n  iat?: number;\n  exp?: number;\n}\n\nexport const hashPassword = async (password: string): Promise<string> => {\n  const saltRounds = 12;\n  return await bcrypt.hash(password, saltRounds);\n};\n\nexport const comparePassword = async (\n  password: string,\n  hashedPassword: string\n): Promise<boolean> => {\n  return await bcrypt.compare(password, hashedPassword);\n};\n\nexport const generateToken = (payload: Omit<JWTPayload, 'iat' | 'exp'>): string => {\n  return jwt.sign(payload, JWT_SECRET, {\n    expiresIn: '7d', // Token expires in 7 days\n  });\n};\n\nexport const verifyToken = (token: string): JWTPayload | null => {\n  try {\n    return jwt.verify(token, JWT_SECRET) as JWTPayload;\n  } catch (error) {\n    return null;\n  }\n};\n\nexport const getTokenFromRequest = (request: NextRequest): string | null => {\n  // Check Authorization header\n  const authHeader = request.headers.get('authorization');\n  if (authHeader && authHeader.startsWith('Bearer ')) {\n    return authHeader.substring(7);\n  }\n\n  // Check cookies\n  const token = request.cookies.get('auth-token')?.value;\n  return token || null;\n};\n\nexport const getUserFromRequest = async (request: NextRequest): Promise<JWTPayload | null> => {\n  const token = getTokenFromRequest(request);\n  if (!token) {\n    return null;\n  }\n\n  return verifyToken(token);\n};\n\n// Middleware helper for protected routes\nexport const requireAuth = (handler: Function) => {\n  return async (request: NextRequest, context: any) => {\n    const user = await getUserFromRequest(request);\n    \n    if (!user) {\n      return new Response(\n        JSON.stringify({ error: 'Authentication required' }),\n        {\n          status: 401,\n          headers: { 'Content-Type': 'application/json' },\n        }\n      );\n    }\n\n    // Add user to request context\n    (request as any).user = user;\n    return handler(request, context);\n  };\n};\n\n// Admin-only middleware\nexport const requireAdmin = (handler: Function) => {\n  return async (request: NextRequest, context: any) => {\n    const user = await getUserFromRequest(request);\n    \n    if (!user) {\n      return new Response(\n        JSON.stringify({ error: 'Authentication required' }),\n        {\n          status: 401,\n          headers: { 'Content-Type': 'application/json' },\n        }\n      );\n    }\n\n    if (user.role !== 'admin') {\n      return new Response(\n        JSON.stringify({ error: 'Admin access required' }),\n        {\n          status: 403,\n          headers: { 'Content-Type': 'application/json' },\n        }\n      );\n    }\n\n    (request as any).user = user;\n    return handler(request, context);\n  };\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAGA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU;AAEzC,IAAI,CAAC,YAAY;IACf,MAAM,IAAI,MAAM;AAClB;AAUO,MAAM,eAAe,OAAO;IACjC,MAAM,aAAa;IACnB,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AACrC;AAEO,MAAM,kBAAkB,OAC7B,UACA;IAEA,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AACxC;AAEO,MAAM,gBAAgB,CAAC;IAC5B,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QACnC,WAAW;IACb;AACF;AAEO,MAAM,cAAc,CAAC;IAC1B,IAAI;QACF,OAAO,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;IAC3B,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAEO,MAAM,sBAAsB,CAAC;IAClC,6BAA6B;IAC7B,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IACvC,IAAI,cAAc,WAAW,UAAU,CAAC,YAAY;QAClD,OAAO,WAAW,SAAS,CAAC;IAC9B;IAEA,gBAAgB;IAChB,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;IACjD,OAAO,SAAS;AAClB;AAEO,MAAM,qBAAqB,OAAO;IACvC,MAAM,QAAQ,oBAAoB;IAClC,IAAI,CAAC,OAAO;QACV,OAAO;IACT;IAEA,OAAO,YAAY;AACrB;AAGO,MAAM,cAAc,CAAC;IAC1B,OAAO,OAAO,SAAsB;QAClC,MAAM,OAAO,MAAM,mBAAmB;QAEtC,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,OAAO;YAA0B,IAClD;gBACE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QAEJ;QAEA,8BAA8B;QAC7B,QAAgB,IAAI,GAAG;QACxB,OAAO,QAAQ,SAAS;IAC1B;AACF;AAGO,MAAM,eAAe,CAAC;IAC3B,OAAO,OAAO,SAAsB;QAClC,MAAM,OAAO,MAAM,mBAAmB;QAEtC,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,OAAO;YAA0B,IAClD;gBACE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QAEJ;QAEA,IAAI,KAAK,IAAI,KAAK,SAAS;YACzB,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,OAAO;YAAwB,IAChD;gBACE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QAEJ;QAEC,QAAgB,IAAI,GAAG;QACxB,OAAO,QAAQ,SAAS;IAC1B;AACF", "debugId": null}}, {"offset": {"line": 379, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/cms/src/lib/cors.ts"], "sourcesContent": ["// CORS middleware for API routes\nimport { NextRequest, NextResponse } from 'next/server';\n\nexport function corsHeaders(origin?: string) {\n  const allowedOrigins = [\n    'http://localhost:3000',\n    'http://localhost:3001',\n    'http://localhost:3002',\n    'http://localhost:3003',\n    'https://your-portfolio-domain.com', // Add your production domain here\n  ];\n\n  const isAllowedOrigin = origin && allowedOrigins.includes(origin);\n\n  return {\n    'Access-Control-Allow-Origin': isAllowedOrigin ? origin : allowedOrigins[0],\n    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n    'Access-Control-Allow-Headers': 'Content-Type, Authorization',\n    'Access-Control-Allow-Credentials': 'true',\n  };\n}\n\nexport function handleCors(request: NextRequest) {\n  const origin = request.headers.get('origin');\n  const headers = corsHeaders(origin || undefined);\n\n  // Handle preflight requests\n  if (request.method === 'OPTIONS') {\n    return new NextResponse(null, { status: 200, headers });\n  }\n\n  return headers;\n}\n\nexport function withCors(handler: (request: NextRequest) => Promise<NextResponse>) {\n  return async (request: NextRequest) => {\n    const origin = request.headers.get('origin');\n    const corsHeadersObj = corsHeaders(origin || undefined);\n\n    // Handle preflight requests\n    if (request.method === 'OPTIONS') {\n      return new NextResponse(null, { status: 200, headers: corsHeadersObj });\n    }\n\n    try {\n      const response = await handler(request);\n\n      // Add CORS headers to the response\n      Object.entries(corsHeadersObj).forEach(([key, value]) => {\n        response.headers.set(key, value);\n      });\n\n      return response;\n    } catch (error) {\n      console.error('API Error:', error);\n      const errorResponse = NextResponse.json(\n        { success: false, message: 'Internal server error' },\n        { status: 500 }\n      );\n\n      // Add CORS headers to error response\n      Object.entries(corsHeadersObj).forEach(([key, value]) => {\n        errorResponse.headers.set(key, value);\n      });\n\n      return errorResponse;\n    }\n  };\n}\n"], "names": [], "mappings": "AAAA,iCAAiC;;;;;;AACjC;;AAEO,SAAS,YAAY,MAAe;IACzC,MAAM,iBAAiB;QACrB;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,kBAAkB,UAAU,eAAe,QAAQ,CAAC;IAE1D,OAAO;QACL,+BAA+B,kBAAkB,SAAS,cAAc,CAAC,EAAE;QAC3E,gCAAgC;QAChC,gCAAgC;QAChC,oCAAoC;IACtC;AACF;AAEO,SAAS,WAAW,OAAoB;IAC7C,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;IACnC,MAAM,UAAU,YAAY,UAAU;IAEtC,4BAA4B;IAC5B,IAAI,QAAQ,MAAM,KAAK,WAAW;QAChC,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;YAAE,QAAQ;YAAK;QAAQ;IACvD;IAEA,OAAO;AACT;AAEO,SAAS,SAAS,OAAwD;IAC/E,OAAO,OAAO;QACZ,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;QACnC,MAAM,iBAAiB,YAAY,UAAU;QAE7C,4BAA4B;QAC5B,IAAI,QAAQ,MAAM,KAAK,WAAW;YAChC,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;gBAAE,QAAQ;gBAAK,SAAS;YAAe;QACvE;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,QAAQ;YAE/B,mCAAmC;YACnC,OAAO,OAAO,CAAC,gBAAgB,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAClD,SAAS,OAAO,CAAC,GAAG,CAAC,KAAK;YAC5B;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,cAAc;YAC5B,MAAM,gBAAgB,gIAAA,CAAA,eAAY,CAAC,IAAI,CACrC;gBAAE,SAAS;gBAAO,SAAS;YAAwB,GACnD;gBAAE,QAAQ;YAAI;YAGhB,qCAAqC;YACrC,OAAO,OAAO,CAAC,gBAAgB,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAClD,cAAc,OAAO,CAAC,GAAG,CAAC,KAAK;YACjC;YAEA,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 455, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/cms/src/app/api/blog/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport connectDB from '@/lib/mongodb';\nimport BlogPost from '@/models/BlogPost';\nimport { requireAuth } from '@/lib/auth';\nimport { withCors } from '@/lib/cors';\nimport slugify from 'slugify';\n\n// GET /api/blog - Get all blog posts with pagination and filters\nexport const GET = withCors(async (request: NextRequest) => {\n  try {\n    await connectDB();\n\n    const { searchParams } = new URL(request.url);\n    const page = parseInt(searchParams.get('page') || '1');\n    const limit = parseInt(searchParams.get('limit') || '10');\n    const status = searchParams.get('status');\n    const category = searchParams.get('category');\n    const featured = searchParams.get('featured');\n    const search = searchParams.get('search');\n\n    // Build query\n    const query: any = {};\n\n    if (status) query.status = status;\n    if (category) query.category = category;\n    if (featured !== null) query.featured = featured === 'true';\n    if (search) {\n      query.$or = [\n        { title: { $regex: search, $options: 'i' } },\n        { excerpt: { $regex: search, $options: 'i' } },\n        { tags: { $in: [new RegExp(search, 'i')] } },\n      ];\n    }\n\n    // Calculate skip\n    const skip = (page - 1) * limit;\n\n    // Get posts with pagination\n    const posts = await BlogPost.find(query)\n      .sort({ createdAt: -1 })\n      .skip(skip)\n      .limit(limit)\n      .lean();\n\n    // Get total count\n    const total = await BlogPost.countDocuments(query);\n\n    return NextResponse.json({\n      success: true,\n      data: posts,\n      pagination: {\n        page,\n        limit,\n        total,\n        pages: Math.ceil(total / limit),\n      },\n    });\n  } catch (error) {\n    console.error('Get blog posts error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n});\n\n// POST /api/blog - Create new blog post\nexport const POST = requireAuth(async (request: NextRequest) => {\n  try {\n    await connectDB();\n\n    const data = await request.json();\n    const {\n      title,\n      excerpt,\n      content,\n      category,\n      tags = [],\n      featured = false,\n      status = 'draft',\n      thumbnail,\n      seoTitle,\n      seoDescription,\n      seoKeywords = [],\n      readTime,\n    } = data;\n\n    // Validate required fields\n    if (!title || !excerpt || !content || !category) {\n      return NextResponse.json(\n        { error: 'Title, excerpt, content, and category are required' },\n        { status: 400 }\n      );\n    }\n\n    // Generate slug\n    const baseSlug = slugify(title, {\n      lower: true,\n      strict: true,\n      remove: /[*+~.()'\"!:@]/g,\n    });\n\n    // Ensure unique slug\n    let slug = baseSlug;\n    let counter = 1;\n    while (await BlogPost.findOne({ slug })) {\n      slug = `${baseSlug}-${counter}`;\n      counter++;\n    }\n\n    // Calculate read time if not provided\n    const calculatedReadTime = readTime || Math.ceil(content.split(' ').length / 200);\n\n    // Create blog post\n    const blogPost = new BlogPost({\n      title,\n      slug,\n      excerpt,\n      content,\n      category,\n      tags,\n      featured,\n      status,\n      thumbnail,\n      seoTitle: seoTitle || title,\n      seoDescription: seoDescription || excerpt,\n      seoKeywords,\n      readTime: calculatedReadTime,\n      publishedAt: status === 'published' ? new Date() : undefined,\n    });\n\n    await blogPost.save();\n\n    return NextResponse.json({\n      success: true,\n      data: blogPost,\n    }, { status: 201 });\n  } catch (error) {\n    console.error('Create blog post error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n});\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAGO,MAAM,MAAM,CAAA,GAAA,oHAAA,CAAA,WAAQ,AAAD,EAAE,OAAO;IACjC,IAAI;QACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,SAAS,aAAa,GAAG,CAAC;QAEhC,cAAc;QACd,MAAM,QAAa,CAAC;QAEpB,IAAI,QAAQ,MAAM,MAAM,GAAG;QAC3B,IAAI,UAAU,MAAM,QAAQ,GAAG;QAC/B,IAAI,aAAa,MAAM,MAAM,QAAQ,GAAG,aAAa;QACrD,IAAI,QAAQ;YACV,MAAM,GAAG,GAAG;gBACV;oBAAE,OAAO;wBAAE,QAAQ;wBAAQ,UAAU;oBAAI;gBAAE;gBAC3C;oBAAE,SAAS;wBAAE,QAAQ;wBAAQ,UAAU;oBAAI;gBAAE;gBAC7C;oBAAE,MAAM;wBAAE,KAAK;4BAAC,IAAI,OAAO,QAAQ;yBAAK;oBAAC;gBAAE;aAC5C;QACH;QAEA,iBAAiB;QACjB,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAE1B,4BAA4B;QAC5B,MAAM,QAAQ,MAAM,2HAAA,CAAA,UAAQ,CAAC,IAAI,CAAC,OAC/B,IAAI,CAAC;YAAE,WAAW,CAAC;QAAE,GACrB,IAAI,CAAC,MACL,KAAK,CAAC,OACN,IAAI;QAEP,kBAAkB;QAClB,MAAM,QAAQ,MAAM,2HAAA,CAAA,UAAQ,CAAC,cAAc,CAAC;QAE5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,YAAY;gBACV;gBACA;gBACA;gBACA,OAAO,KAAK,IAAI,CAAC,QAAQ;YAC3B;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,MAAM,OAAO,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD,EAAE,OAAO;IACrC,IAAI;QACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,KAAK,EACL,OAAO,EACP,OAAO,EACP,QAAQ,EACR,OAAO,EAAE,EACT,WAAW,KAAK,EAChB,SAAS,OAAO,EAChB,SAAS,EACT,QAAQ,EACR,cAAc,EACd,cAAc,EAAE,EAChB,QAAQ,EACT,GAAG;QAEJ,2BAA2B;QAC3B,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,WAAW,CAAC,UAAU;YAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAqD,GAC9D;gBAAE,QAAQ;YAAI;QAElB;QAEA,gBAAgB;QAChB,MAAM,WAAW,CAAA,GAAA,oIAAA,CAAA,UAAO,AAAD,EAAE,OAAO;YAC9B,OAAO;YACP,QAAQ;YACR,QAAQ;QACV;QAEA,qBAAqB;QACrB,IAAI,OAAO;QACX,IAAI,UAAU;QACd,MAAO,MAAM,2HAAA,CAAA,UAAQ,CAAC,OAAO,CAAC;YAAE;QAAK,GAAI;YACvC,OAAO,GAAG,SAAS,CAAC,EAAE,SAAS;YAC/B;QACF;QAEA,sCAAsC;QACtC,MAAM,qBAAqB,YAAY,KAAK,IAAI,CAAC,QAAQ,KAAK,CAAC,KAAK,MAAM,GAAG;QAE7E,mBAAmB;QACnB,MAAM,WAAW,IAAI,2HAAA,CAAA,UAAQ,CAAC;YAC5B;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,UAAU,YAAY;YACtB,gBAAgB,kBAAkB;YAClC;YACA,UAAU;YACV,aAAa,WAAW,cAAc,IAAI,SAAS;QACrD;QAEA,MAAM,SAAS,IAAI;QAEnB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR,GAAG;YAAE,QAAQ;QAAI;IACnB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}