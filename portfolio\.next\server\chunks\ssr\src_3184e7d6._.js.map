{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/ScrollToTop.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { ArrowUp } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\n\nexport default function ScrollToTop() {\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    const toggleVisibility = () => {\n      if (window.pageYOffset > 300) {\n        setIsVisible(true);\n      } else {\n        setIsVisible(false);\n      }\n    };\n\n    window.addEventListener('scroll', toggleVisibility);\n    return () => window.removeEventListener('scroll', toggleVisibility);\n  }, []);\n\n  const scrollToTop = () => {\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth',\n    });\n  };\n\n  if (!isVisible) {\n    return null;\n  }\n\n  return (\n    <Button\n      onClick={scrollToTop}\n      className=\"fixed bottom-8 right-8 z-50 w-12 h-12 bg-portfolio-secondary hover:bg-portfolio-secondary/90 text-white rounded-full shadow-lg hover:scale-110 transition-all duration-300\"\n      aria-label=\"Scroll to top\"\n    >\n      <ArrowUp size={20} />\n    </Button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,IAAI,OAAO,WAAW,GAAG,KAAK;gBAC5B,aAAa;YACf,OAAO;gBACL,aAAa;YACf;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC;YACd,KAAK;YACL,UAAU;QACZ;IACF;IAEA,IAAI,CAAC,WAAW;QACd,OAAO;IACT;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,SAAS;QACT,WAAU;QACV,cAAW;kBAEX,cAAA,8OAAC,4MAAA,CAAA,UAAO;YAAC,MAAM;;;;;;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 64, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\">\n          <XIcon />\n          <span className=\"sr-only\">Close</span>\n        </DialogPrimitive.Close>\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,GAAG,OACkD;IACrD,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 237, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 283, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 508, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/lib/api.ts"], "sourcesContent": ["// API integration for fetching content from CMS\nconst CMS_BASE_URL = process.env.NEXT_PUBLIC_CMS_URL || \"http://localhost:3002\";\n\n// Types for API responses\nexport interface BlogPost {\n  _id: string;\n  title: string;\n  slug: string;\n  excerpt: string;\n  content: string;\n  thumbnail: string;\n  category: string;\n  tags: string[];\n  featured: boolean;\n  status: \"draft\" | \"published\" | \"archived\";\n  readTime: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Video {\n  _id: string;\n  id: string;\n  title: string;\n  slug: string;\n  description?: string;\n  category?: string;\n  tags: string[];\n  featured: boolean;\n  status: \"draft\" | \"published\" | \"archived\";\n  order: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Reel {\n  _id: string;\n  id: string;\n  title: string;\n  slug: string;\n  thumbnail?: string; // Generated dynamically from YouTube API\n  description?: string;\n  platform: \"youtube\";\n  embedUrl?: string;\n  featured: boolean;\n  status: \"draft\" | \"published\" | \"archived\";\n  order: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Testimonial {\n  _id: string;\n  name: string;\n  slug: string;\n  avatar?: string;\n  role: string;\n  company?: string;\n  email?: string;\n  linkedinUrl?: string;\n  content: string;\n  rating: number;\n  featured: boolean;\n  status: \"draft\" | \"published\" | \"archived\";\n  order: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Client {\n  _id: string;\n  name: string;\n  slug: string;\n  logo: string;\n  description: string;\n  website?: string;\n  industry?: string;\n  projectType?: string;\n  featured: boolean;\n  status: \"draft\" | \"published\" | \"archived\";\n  order: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// API response wrapper\ninterface ApiResponse<T> {\n  success: boolean;\n  data: T;\n  message?: string;\n}\n\n// Generic fetch function with error handling\nasync function fetchFromCMS<T>(endpoint: string): Promise<T[]> {\n  try {\n    const response = await fetch(`${CMS_BASE_URL}/api${endpoint}`, {\n      method: \"GET\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      // Add cache control for better performance\n      next: { revalidate: 300 }, // Revalidate every 5 minutes\n    });\n\n    if (!response.ok) {\n      console.error(\n        `Failed to fetch ${endpoint}:`,\n        response.status,\n        response.statusText\n      );\n      return [];\n    }\n\n    const result: ApiResponse<T[]> = await response.json();\n\n    if (!result.success) {\n      console.error(`API error for ${endpoint}:`, result.message);\n      return [];\n    }\n\n    return result.data || [];\n  } catch (error) {\n    console.error(`Network error fetching ${endpoint}:`, error);\n    return [];\n  }\n}\n\n// Blog API functions\nexport async function getBlogPosts(): Promise<BlogPost[]> {\n  const posts = await fetchFromCMS<BlogPost>(\"/blog\");\n  // Only return published posts, sorted by creation date\n  return posts\n    .filter((post) => post.status === \"published\")\n    .sort(\n      (a, b) =>\n        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()\n    );\n}\n\nexport async function getFeaturedBlogPosts(): Promise<BlogPost[]> {\n  const posts = await getBlogPosts();\n  return posts.filter((post) => post.featured);\n}\n\nexport async function getBlogPostBySlug(\n  slug: string\n): Promise<BlogPost | null> {\n  try {\n    const response = await fetch(`${CMS_BASE_URL}/api/blog/slug/${slug}`, {\n      method: \"GET\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      next: { revalidate: 300 },\n    });\n\n    if (!response.ok) {\n      return null;\n    }\n\n    const result: ApiResponse<BlogPost> = await response.json();\n    return result.success ? result.data : null;\n  } catch (error) {\n    console.error(\"Error fetching blog post by slug:\", error);\n    return null;\n  }\n}\n\n// Videos API functions\nexport async function getVideos(): Promise<Video[]> {\n  const videos = await fetchFromCMS<Video>(\"/videos\");\n  return videos\n    .filter((video) => video.status === \"published\")\n    .sort((a, b) => a.order - b.order);\n}\n\nexport async function getFeaturedVideos(): Promise<Video[]> {\n  const videos = await getVideos();\n  return videos.filter((video) => video.featured);\n}\n\n// Reels API functions\nexport async function getReels(): Promise<Reel[]> {\n  const reels = await fetchFromCMS<Reel>(\"/reels\");\n  return reels\n    .filter((reel) => reel.status === \"published\")\n    .sort((a, b) => a.order - b.order);\n}\n\nexport async function getFeaturedReels(): Promise<Reel[]> {\n  const reels = await getReels();\n  return reels.filter((reel) => reel.featured);\n}\n\n// Testimonials API functions\nexport async function getTestimonials(): Promise<Testimonial[]> {\n  const testimonials = await fetchFromCMS<Testimonial>(\"/testimonials\");\n  return testimonials\n    .filter((testimonial) => testimonial.status === \"published\")\n    .sort((a, b) => a.order - b.order);\n}\n\nexport async function getFeaturedTestimonials(): Promise<Testimonial[]> {\n  const testimonials = await getTestimonials();\n  return testimonials.filter((testimonial) => testimonial.featured);\n}\n\n// Clients API functions\nexport async function getClients(): Promise<Client[]> {\n  const clients = await fetchFromCMS<Client>(\"/clients\");\n  return clients\n    .filter((client) => client.status === \"published\")\n    .sort((a, b) => a.order - b.order);\n}\n\nexport async function getFeaturedClients(): Promise<Client[]> {\n  const clients = await getClients();\n  return clients.filter((client) => client.featured);\n}\n\n// Utility functions\nexport function getYouTubeThumbnail(videoId: string): string {\n  return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n}\n\nexport function generateEmbedUrl(id: string): string {\n  // Only YouTube Shorts are supported now\n  return `https://www.youtube.com/embed/${id}`;\n}\n\nexport function getYouTubeShortsThumbnail(videoId: string): string {\n  return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n}\n\n// Helper function to ensure reels have thumbnails\nexport function ensureReelThumbnail(reel: Reel): Reel {\n  return {\n    ...reel,\n    thumbnail: reel.thumbnail || getYouTubeShortsThumbnail(reel.id),\n  };\n}\n\nexport function formatDate(dateString: string): string {\n  return new Date(dateString).toLocaleDateString(\"en-US\", {\n    year: \"numeric\",\n    month: \"long\",\n    day: \"numeric\",\n  });\n}\n\nexport function calculateReadTime(content: string): number {\n  const wordsPerMinute = 200;\n  const wordCount = content.split(/\\s+/).length;\n  return Math.ceil(wordCount / wordsPerMinute);\n}\n"], "names": [], "mappings": "AAAA,gDAAgD;;;;;;;;;;;;;;;;;;;;AAChD,MAAM,eAAe,wEAAmC;AA2FxD,6CAA6C;AAC7C,eAAe,aAAgB,QAAgB;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,IAAI,EAAE,UAAU,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,2CAA2C;YAC3C,MAAM;gBAAE,YAAY;YAAI;QAC1B;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CACX,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC,EAC9B,SAAS,MAAM,EACf,SAAS,UAAU;YAErB,OAAO,EAAE;QACX;QAEA,MAAM,SAA2B,MAAM,SAAS,IAAI;QAEpD,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC,EAAE,OAAO,OAAO;YAC1D,OAAO,EAAE;QACX;QAEA,OAAO,OAAO,IAAI,IAAI,EAAE;IAC1B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,SAAS,CAAC,CAAC,EAAE;QACrD,OAAO,EAAE;IACX;AACF;AAGO,eAAe;IACpB,MAAM,QAAQ,MAAM,aAAuB;IAC3C,uDAAuD;IACvD,OAAO,MACJ,MAAM,CAAC,CAAC,OAAS,KAAK,MAAM,KAAK,aACjC,IAAI,CACH,CAAC,GAAG,IACF,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;AAEvE;AAEO,eAAe;IACpB,MAAM,QAAQ,MAAM;IACpB,OAAO,MAAM,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ;AAC7C;AAEO,eAAe,kBACpB,IAAY;IAEZ,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,eAAe,EAAE,MAAM,EAAE;YACpE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM;gBAAE,YAAY;YAAI;QAC1B;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,OAAO;QACT;QAEA,MAAM,SAAgC,MAAM,SAAS,IAAI;QACzD,OAAO,OAAO,OAAO,GAAG,OAAO,IAAI,GAAG;IACxC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO;IACT;AACF;AAGO,eAAe;IACpB,MAAM,SAAS,MAAM,aAAoB;IACzC,OAAO,OACJ,MAAM,CAAC,CAAC,QAAU,MAAM,MAAM,KAAK,aACnC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AACrC;AAEO,eAAe;IACpB,MAAM,SAAS,MAAM;IACrB,OAAO,OAAO,MAAM,CAAC,CAAC,QAAU,MAAM,QAAQ;AAChD;AAGO,eAAe;IACpB,MAAM,QAAQ,MAAM,aAAmB;IACvC,OAAO,MACJ,MAAM,CAAC,CAAC,OAAS,KAAK,MAAM,KAAK,aACjC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AACrC;AAEO,eAAe;IACpB,MAAM,QAAQ,MAAM;IACpB,OAAO,MAAM,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ;AAC7C;AAGO,eAAe;IACpB,MAAM,eAAe,MAAM,aAA0B;IACrD,OAAO,aACJ,MAAM,CAAC,CAAC,cAAgB,YAAY,MAAM,KAAK,aAC/C,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AACrC;AAEO,eAAe;IACpB,MAAM,eAAe,MAAM;IAC3B,OAAO,aAAa,MAAM,CAAC,CAAC,cAAgB,YAAY,QAAQ;AAClE;AAGO,eAAe;IACpB,MAAM,UAAU,MAAM,aAAqB;IAC3C,OAAO,QACJ,MAAM,CAAC,CAAC,SAAW,OAAO,MAAM,KAAK,aACrC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AACrC;AAEO,eAAe;IACpB,MAAM,UAAU,MAAM;IACtB,OAAO,QAAQ,MAAM,CAAC,CAAC,SAAW,OAAO,QAAQ;AACnD;AAGO,SAAS,oBAAoB,OAAe;IACjD,OAAO,CAAC,2BAA2B,EAAE,QAAQ,kBAAkB,CAAC;AAClE;AAEO,SAAS,iBAAiB,EAAU;IACzC,wCAAwC;IACxC,OAAO,CAAC,8BAA8B,EAAE,IAAI;AAC9C;AAEO,SAAS,0BAA0B,OAAe;IACvD,OAAO,CAAC,2BAA2B,EAAE,QAAQ,kBAAkB,CAAC;AAClE;AAGO,SAAS,oBAAoB,IAAU;IAC5C,OAAO;QACL,GAAG,IAAI;QACP,WAAW,KAAK,SAAS,IAAI,0BAA0B,KAAK,EAAE;IAChE;AACF;AAEO,SAAS,WAAW,UAAkB;IAC3C,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;QACtD,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,kBAAkB,OAAe;IAC/C,MAAM,iBAAiB;IACvB,MAAM,YAAY,QAAQ,KAAK,CAAC,OAAO,MAAM;IAC7C,OAAO,KAAK,IAAI,CAAC,YAAY;AAC/B", "debugId": null}}, {"offset": {"line": 653, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/app/videos/VideosPageClient.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { ArrowLeft, Play, X, Filter, Grid, List } from 'lucide-react';\nimport { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { getYouTubeThumbnail, type Video } from '@/lib/api';\n\ninterface VideosPageClientProps {\n  allVideos: Video[];\n  featuredVideos: Video[];\n}\n\nexport default function VideosPageClient({ allVideos, featuredVideos }: VideosPageClientProps) {\n  const [selectedVideo, setSelectedVideo] = useState<string | null>(null);\n  const [filteredVideos, setFilteredVideos] = useState<Video[]>(allVideos);\n  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');\n  const [categoryFilter, setCategoryFilter] = useState<string>('all');\n\n  // Get unique categories\n  const categories = Array.from(new Set(allVideos.map(video => video.category).filter(Boolean)));\n\n  const getYouTubeEmbedUrl = (videoId: string) => {\n    return `https://www.youtube.com/embed/${videoId}?autoplay=1&rel=0&modestbranding=1`;\n  };\n\n  const handleCategoryFilter = (category: string) => {\n    setCategoryFilter(category);\n    if (category === 'all') {\n      setFilteredVideos(allVideos);\n    } else {\n      setFilteredVideos(allVideos.filter(video => video.category === category));\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-portfolio-light\">\n      {/* Header */}\n      <div className=\"bg-portfolio-primary text-white py-20\">\n        <div className=\"container mx-auto px-4\">\n          <Link \n            href=\"/#featured-work\"\n            className=\"inline-flex items-center gap-2 text-portfolio-accent hover:text-white transition-colors mb-8\"\n          >\n            <ArrowLeft size={20} />\n            Back to Portfolio\n          </Link>\n          \n          <h1 className=\"text-4xl md:text-5xl font-heading font-bold mb-4\">\n            Video Portfolio\n          </h1>\n          <p className=\"text-xl text-white/90 max-w-2xl\">\n            Explore my complete collection of video editing work, from creative projects to professional tutorials.\n          </p>\n        </div>\n      </div>\n\n      <div className=\"container mx-auto px-4 py-16\">\n        {/* Featured Videos */}\n        {featuredVideos.length > 0 && (\n          <section className=\"mb-16\">\n            <h2 className=\"text-3xl font-heading font-bold text-portfolio-primary mb-8\">\n              Featured Videos\n            </h2>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n              {featuredVideos.map((video) => (\n                <Dialog key={video._id}>\n                  <DialogTrigger asChild>\n                    <div\n                      onClick={() => setSelectedVideo(video.id)}\n                      className=\"group bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105 cursor-pointer\"\n                    >\n                      <div className=\"relative aspect-video overflow-hidden\">\n                        <Image\n                          src={getYouTubeThumbnail(video.id)}\n                          alt={video.title}\n                          fill\n                          className=\"object-cover transition-transform duration-500 group-hover:scale-110\"\n                        />\n                        \n                        <div className=\"absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-colors duration-300 flex items-center justify-center\">\n                          <div className=\"bg-white/90 backdrop-blur-sm rounded-full p-4 group-hover:scale-110 transition-transform duration-300\">\n                            <Play className=\"h-8 w-8 text-portfolio-primary ml-1\" fill=\"currentColor\" />\n                          </div>\n                        </div>\n\n                        <div className=\"absolute top-4 left-4\">\n                          <Badge variant=\"default\" className=\"bg-portfolio-accent text-white\">\n                            Featured\n                          </Badge>\n                        </div>\n\n                        {video.category && (\n                          <div className=\"absolute top-4 right-4\">\n                            <Badge variant=\"secondary\" className=\"bg-portfolio-secondary/90 text-white\">\n                              {video.category}\n                            </Badge>\n                          </div>\n                        )}\n                      </div>\n\n                      <div className=\"p-6\">\n                        <h3 className=\"text-xl font-heading font-semibold text-portfolio-primary mb-3 group-hover:text-portfolio-secondary transition-colors duration-300\">\n                          {video.title}\n                        </h3>\n\n                        {video.description && (\n                          <p className=\"text-muted-foreground text-sm leading-relaxed mb-4 line-clamp-2\">\n                            {video.description}\n                          </p>\n                        )}\n\n                        {video.tags && video.tags.length > 0 && (\n                          <div className=\"flex flex-wrap gap-2\">\n                            {video.tags.slice(0, 3).map((tag) => (\n                              <Badge key={tag} variant=\"outline\" className=\"text-xs\">\n                                {tag}\n                              </Badge>\n                            ))}\n                            {video.tags.length > 3 && (\n                              <Badge variant=\"outline\" className=\"text-xs\">\n                                +{video.tags.length - 3}\n                              </Badge>\n                            )}\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  </DialogTrigger>\n\n                  <DialogContent className=\"max-w-4xl w-full p-0\">\n                    <div className=\"relative aspect-video\">\n                      <button\n                        onClick={() => setSelectedVideo(null)}\n                        className=\"absolute top-4 right-4 z-10 bg-black/50 hover:bg-black/70 text-white rounded-full p-2 transition-colors\"\n                      >\n                        <X size={20} />\n                      </button>\n                      \n                      {selectedVideo === video.id && (\n                        <iframe\n                          src={getYouTubeEmbedUrl(video.id)}\n                          title={video.title}\n                          className=\"w-full h-full rounded-lg\"\n                          allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n                          allowFullScreen\n                        />\n                      )}\n                    </div>\n                  </DialogContent>\n                </Dialog>\n              ))}\n            </div>\n          </section>\n        )}\n\n        {/* Filters and View Controls */}\n        <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8\">\n          <div className=\"flex items-center gap-4\">\n            <div className=\"flex items-center gap-2\">\n              <Filter size={20} className=\"text-portfolio-primary\" />\n              <span className=\"font-medium text-portfolio-primary\">Filter:</span>\n            </div>\n            <Select value={categoryFilter} onValueChange={handleCategoryFilter}>\n              <SelectTrigger className=\"w-48\">\n                <SelectValue placeholder=\"Select category\" />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"all\">All Categories</SelectItem>\n                {categories.map((category) => (\n                  <SelectItem key={category} value={category}>\n                    {category}\n                  </SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n          </div>\n\n          <div className=\"flex items-center gap-2\">\n            <Button\n              variant={viewMode === 'grid' ? 'default' : 'outline'}\n              size=\"sm\"\n              onClick={() => setViewMode('grid')}\n            >\n              <Grid size={16} />\n            </Button>\n            <Button\n              variant={viewMode === 'list' ? 'default' : 'outline'}\n              size=\"sm\"\n              onClick={() => setViewMode('list')}\n            >\n              <List size={16} />\n            </Button>\n          </div>\n        </div>\n\n        {/* All Videos */}\n        <section>\n          <h2 className=\"text-3xl font-heading font-bold text-portfolio-primary mb-8\">\n            {categoryFilter === 'all' ? 'All Videos' : `${categoryFilter} Videos`}\n            <span className=\"text-lg font-normal text-muted-foreground ml-2\">\n              ({filteredVideos.length} videos)\n            </span>\n          </h2>\n          \n          {filteredVideos.length === 0 ? (\n            <div className=\"text-center py-12\">\n              <p className=\"text-lg text-muted-foreground\">No videos found for the selected category.</p>\n            </div>\n          ) : (\n            <div className={viewMode === 'grid' \n              ? \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\" \n              : \"space-y-6\"\n            }>\n              {filteredVideos.map((video) => (\n                <Dialog key={video._id}>\n                  <DialogTrigger asChild>\n                    <div\n                      onClick={() => setSelectedVideo(video.id)}\n                      className={`group bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105 cursor-pointer ${\n                        viewMode === 'list' ? 'flex' : ''\n                      }`}\n                    >\n                      <div className={`relative overflow-hidden ${\n                        viewMode === 'list' ? 'w-80 aspect-video' : 'aspect-video'\n                      }`}>\n                        <Image\n                          src={getYouTubeThumbnail(video.id)}\n                          alt={video.title}\n                          fill\n                          className=\"object-cover transition-transform duration-500 group-hover:scale-110\"\n                        />\n                        \n                        <div className=\"absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-colors duration-300 flex items-center justify-center\">\n                          <div className=\"bg-white/90 backdrop-blur-sm rounded-full p-4 group-hover:scale-110 transition-transform duration-300\">\n                            <Play className=\"h-8 w-8 text-portfolio-primary ml-1\" fill=\"currentColor\" />\n                          </div>\n                        </div>\n\n                        {video.featured && (\n                          <div className=\"absolute top-4 left-4\">\n                            <Badge variant=\"default\" className=\"bg-portfolio-accent text-white\">\n                              Featured\n                            </Badge>\n                          </div>\n                        )}\n\n                        {video.category && (\n                          <div className=\"absolute top-4 right-4\">\n                            <Badge variant=\"secondary\" className=\"bg-portfolio-secondary/90 text-white\">\n                              {video.category}\n                            </Badge>\n                          </div>\n                        )}\n                      </div>\n\n                      <div className=\"p-6 flex-1\">\n                        <h3 className=\"text-xl font-heading font-semibold text-portfolio-primary mb-3 group-hover:text-portfolio-secondary transition-colors duration-300\">\n                          {video.title}\n                        </h3>\n\n                        {video.description && (\n                          <p className=\"text-muted-foreground text-sm leading-relaxed mb-4 line-clamp-3\">\n                            {video.description}\n                          </p>\n                        )}\n\n                        {video.tags && video.tags.length > 0 && (\n                          <div className=\"flex flex-wrap gap-2\">\n                            {video.tags.slice(0, viewMode === 'list' ? 5 : 3).map((tag) => (\n                              <Badge key={tag} variant=\"outline\" className=\"text-xs\">\n                                {tag}\n                              </Badge>\n                            ))}\n                            {video.tags.length > (viewMode === 'list' ? 5 : 3) && (\n                              <Badge variant=\"outline\" className=\"text-xs\">\n                                +{video.tags.length - (viewMode === 'list' ? 5 : 3)}\n                              </Badge>\n                            )}\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  </DialogTrigger>\n\n                  <DialogContent className=\"max-w-4xl w-full p-0\">\n                    <div className=\"relative aspect-video\">\n                      <button\n                        onClick={() => setSelectedVideo(null)}\n                        className=\"absolute top-4 right-4 z-10 bg-black/50 hover:bg-black/70 text-white rounded-full p-2 transition-colors\"\n                      >\n                        <X size={20} />\n                      </button>\n                      \n                      {selectedVideo === video.id && (\n                        <iframe\n                          src={getYouTubeEmbedUrl(video.id)}\n                          title={video.title}\n                          className=\"w-full h-full rounded-lg\"\n                          allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n                          allowFullScreen\n                        />\n                      )}\n                    </div>\n                  </DialogContent>\n                </Dialog>\n              ))}\n            </div>\n          )}\n        </section>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;;AAiBe,SAAS,iBAAiB,EAAE,SAAS,EAAE,cAAc,EAAyB;IAC3F,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAC9D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE7D,wBAAwB;IACxB,MAAM,aAAa,MAAM,IAAI,CAAC,IAAI,IAAI,UAAU,GAAG,CAAC,CAAA,QAAS,MAAM,QAAQ,EAAE,MAAM,CAAC;IAEpF,MAAM,qBAAqB,CAAC;QAC1B,OAAO,CAAC,8BAA8B,EAAE,QAAQ,kCAAkC,CAAC;IACrF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,kBAAkB;QAClB,IAAI,aAAa,OAAO;YACtB,kBAAkB;QACpB,OAAO;YACL,kBAAkB,UAAU,MAAM,CAAC,CAAA,QAAS,MAAM,QAAQ,KAAK;QACjE;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,8OAAC,gNAAA,CAAA,YAAS;oCAAC,MAAM;;;;;;gCAAM;;;;;;;sCAIzB,8OAAC;4BAAG,WAAU;sCAAmD;;;;;;sCAGjE,8OAAC;4BAAE,WAAU;sCAAkC;;;;;;;;;;;;;;;;;0BAMnD,8OAAC;gBAAI,WAAU;;oBAEZ,eAAe,MAAM,GAAG,mBACvB,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAG,WAAU;0CAA8D;;;;;;0CAG5E,8OAAC;gCAAI,WAAU;0CACZ,eAAe,GAAG,CAAC,CAAC,sBACnB,8OAAC,kIAAA,CAAA,SAAM;;0DACL,8OAAC,kIAAA,CAAA,gBAAa;gDAAC,OAAO;0DACpB,cAAA,8OAAC;oDACC,SAAS,IAAM,iBAAiB,MAAM,EAAE;oDACxC,WAAU;;sEAEV,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,6HAAA,CAAA,UAAK;oEACJ,KAAK,CAAA,GAAA,iHAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,EAAE;oEACjC,KAAK,MAAM,KAAK;oEAChB,IAAI;oEACJ,WAAU;;;;;;8EAGZ,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4EAAC,WAAU;4EAAsC,MAAK;;;;;;;;;;;;;;;;8EAI/D,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAU,WAAU;kFAAiC;;;;;;;;;;;gEAKrE,MAAM,QAAQ,kBACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAY,WAAU;kFAClC,MAAM,QAAQ;;;;;;;;;;;;;;;;;sEAMvB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EACX,MAAM,KAAK;;;;;;gEAGb,MAAM,WAAW,kBAChB,8OAAC;oEAAE,WAAU;8EACV,MAAM,WAAW;;;;;;gEAIrB,MAAM,IAAI,IAAI,MAAM,IAAI,CAAC,MAAM,GAAG,mBACjC,8OAAC;oEAAI,WAAU;;wEACZ,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBAC3B,8OAAC,iIAAA,CAAA,QAAK;gFAAW,SAAQ;gFAAU,WAAU;0FAC1C;+EADS;;;;;wEAIb,MAAM,IAAI,CAAC,MAAM,GAAG,mBACnB,8OAAC,iIAAA,CAAA,QAAK;4EAAC,SAAQ;4EAAU,WAAU;;gFAAU;gFACzC,MAAM,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DASpC,8OAAC,kIAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,SAAS,IAAM,iBAAiB;4DAChC,WAAU;sEAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gEAAC,MAAM;;;;;;;;;;;wDAGV,kBAAkB,MAAM,EAAE,kBACzB,8OAAC;4DACC,KAAK,mBAAmB,MAAM,EAAE;4DAChC,OAAO,MAAM,KAAK;4DAClB,WAAU;4DACV,OAAM;4DACN,eAAe;;;;;;;;;;;;;;;;;;uCA/EZ,MAAM,GAAG;;;;;;;;;;;;;;;;kCA2F9B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,MAAM;gDAAI,WAAU;;;;;;0DAC5B,8OAAC;gDAAK,WAAU;0DAAqC;;;;;;;;;;;;kDAEvD,8OAAC,kIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAgB,eAAe;;0DAC5C,8OAAC,kIAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,8OAAC,kIAAA,CAAA,gBAAa;;kEACZ,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;oDACvB,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,kIAAA,CAAA,aAAU;4DAAgB,OAAO;sEAC/B;2DADc;;;;;;;;;;;;;;;;;;;;;;;0CAQzB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,aAAa,SAAS,YAAY;wCAC3C,MAAK;wCACL,SAAS,IAAM,YAAY;kDAE3B,cAAA,8OAAC,yMAAA,CAAA,OAAI;4CAAC,MAAM;;;;;;;;;;;kDAEd,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,aAAa,SAAS,YAAY;wCAC3C,MAAK;wCACL,SAAS,IAAM,YAAY;kDAE3B,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAMlB,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;;oCACX,mBAAmB,QAAQ,eAAe,GAAG,eAAe,OAAO,CAAC;kDACrE,8OAAC;wCAAK,WAAU;;4CAAiD;4CAC7D,eAAe,MAAM;4CAAC;;;;;;;;;;;;;4BAI3B,eAAe,MAAM,KAAK,kBACzB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;qDAG/C,8OAAC;gCAAI,WAAW,aAAa,SACzB,yDACA;0CAED,eAAe,GAAG,CAAC,CAAC,sBACnB,8OAAC,kIAAA,CAAA,SAAM;;0DACL,8OAAC,kIAAA,CAAA,gBAAa;gDAAC,OAAO;0DACpB,cAAA,8OAAC;oDACC,SAAS,IAAM,iBAAiB,MAAM,EAAE;oDACxC,WAAW,CAAC,gIAAgI,EAC1I,aAAa,SAAS,SAAS,IAC/B;;sEAEF,8OAAC;4DAAI,WAAW,CAAC,yBAAyB,EACxC,aAAa,SAAS,sBAAsB,gBAC5C;;8EACA,8OAAC,6HAAA,CAAA,UAAK;oEACJ,KAAK,CAAA,GAAA,iHAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,EAAE;oEACjC,KAAK,MAAM,KAAK;oEAChB,IAAI;oEACJ,WAAU;;;;;;8EAGZ,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4EAAC,WAAU;4EAAsC,MAAK;;;;;;;;;;;;;;;;gEAI9D,MAAM,QAAQ,kBACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAU,WAAU;kFAAiC;;;;;;;;;;;gEAMvE,MAAM,QAAQ,kBACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAY,WAAU;kFAClC,MAAM,QAAQ;;;;;;;;;;;;;;;;;sEAMvB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EACX,MAAM,KAAK;;;;;;gEAGb,MAAM,WAAW,kBAChB,8OAAC;oEAAE,WAAU;8EACV,MAAM,WAAW;;;;;;gEAIrB,MAAM,IAAI,IAAI,MAAM,IAAI,CAAC,MAAM,GAAG,mBACjC,8OAAC;oEAAI,WAAU;;wEACZ,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,aAAa,SAAS,IAAI,GAAG,GAAG,CAAC,CAAC,oBACrD,8OAAC,iIAAA,CAAA,QAAK;gFAAW,SAAQ;gFAAU,WAAU;0FAC1C;+EADS;;;;;wEAIb,MAAM,IAAI,CAAC,MAAM,GAAG,CAAC,aAAa,SAAS,IAAI,CAAC,mBAC/C,8OAAC,iIAAA,CAAA,QAAK;4EAAC,SAAQ;4EAAU,WAAU;;gFAAU;gFACzC,MAAM,IAAI,CAAC,MAAM,GAAG,CAAC,aAAa,SAAS,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAShE,8OAAC,kIAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,SAAS,IAAM,iBAAiB;4DAChC,WAAU;sEAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gEAAC,MAAM;;;;;;;;;;;wDAGV,kBAAkB,MAAM,EAAE,kBACzB,8OAAC;4DACC,KAAK,mBAAmB,MAAM,EAAE;4DAChC,OAAO,MAAM,KAAK;4DAClB,WAAU;4DACV,OAAM;4DACN,eAAe;;;;;;;;;;;;;;;;;;uCArFZ,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkGtC", "debugId": null}}]}