{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\nexport function middleware(request: NextRequest) {\n  // Handle CORS for API routes\n  if (request.nextUrl.pathname.startsWith('/api/')) {\n    const allowedOrigins = [\n      'http://localhost:3000',\n      'http://localhost:3001', \n      'http://localhost:3002',\n      'https://your-portfolio-domain.com', // Add your production domain here\n    ];\n\n    const origin = request.headers.get('origin');\n    const isAllowedOrigin = origin && allowedOrigins.includes(origin);\n\n    // Handle preflight requests\n    if (request.method === 'OPTIONS') {\n      return new NextResponse(null, {\n        status: 200,\n        headers: {\n          'Access-Control-Allow-Origin': isAllowedOrigin ? origin : allowedOrigins[0],\n          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n          'Access-Control-Allow-Headers': 'Content-Type, Authorization',\n          'Access-Control-Allow-Credentials': 'true',\n        },\n      });\n    }\n\n    // Add CORS headers to all API responses\n    const response = NextResponse.next();\n    response.headers.set('Access-Control-Allow-Origin', isAllowedOrigin ? origin : allowedOrigins[0]);\n    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');\n    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');\n    response.headers.set('Access-Control-Allow-Credentials', 'true');\n\n    return response;\n  }\n\n  return NextResponse.next();\n}\n\nexport const config = {\n  matcher: '/api/:path*',\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAEO,SAAS,WAAW,OAAoB;IAC7C,6BAA6B;IAC7B,IAAI,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,UAAU;QAChD,MAAM,iBAAiB;YACrB;YACA;YACA;YACA;SACD;QAED,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;QACnC,MAAM,kBAAkB,UAAU,eAAe,QAAQ,CAAC;QAE1D,4BAA4B;QAC5B,IAAI,QAAQ,MAAM,KAAK,WAAW;YAChC,OAAO,IAAI,6LAAA,CAAA,eAAY,CAAC,MAAM;gBAC5B,QAAQ;gBACR,SAAS;oBACP,+BAA+B,kBAAkB,SAAS,cAAc,CAAC,EAAE;oBAC3E,gCAAgC;oBAChC,gCAAgC;oBAChC,oCAAoC;gBACtC;YACF;QACF;QAEA,wCAAwC;QACxC,MAAM,WAAW,6LAAA,CAAA,eAAY,CAAC,IAAI;QAClC,SAAS,OAAO,CAAC,GAAG,CAAC,+BAA+B,kBAAkB,SAAS,cAAc,CAAC,EAAE;QAChG,SAAS,OAAO,CAAC,GAAG,CAAC,gCAAgC;QACrD,SAAS,OAAO,CAAC,GAAG,CAAC,gCAAgC;QACrD,SAAS,OAAO,CAAC,GAAG,CAAC,oCAAoC;QAEzD,OAAO;IACT;IAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;AACX"}}]}