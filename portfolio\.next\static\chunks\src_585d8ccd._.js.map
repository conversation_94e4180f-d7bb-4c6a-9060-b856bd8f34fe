{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { Menu, X, ChevronDown } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\n\nconst navigation = [\n  { name: 'Home', href: '#home' },\n  {\n    name: 'Videos',\n    href: '#featured-work',\n    dropdown: [\n      { name: 'Featured Videos', href: '#featured-work' },\n      { name: 'View All Videos', href: '/videos' }\n    ]\n  },\n  { name: 'Clients', href: '#clients' },\n  {\n    name: '<PERSON><PERSON>',\n    href: '#reels',\n    dropdown: [\n      { name: 'Featured Reels', href: '#reels' },\n      { name: 'View All Reels', href: '/reels' }\n    ]\n  },\n  {\n    name: 'Blog',\n    href: '#blog',\n    dropdown: [\n      { name: 'Latest Posts', href: '#blog' },\n      { name: 'View All Posts', href: '/blog' }\n    ]\n  },\n  { name: 'Testimonials', href: '#testimonials' },\n  { name: 'Contact', href: '#contact' },\n];\n\nexport default function Header() {\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [activeSection, setActiveSection] = useState('home');\n  const [openDropdown, setOpenDropdown] = useState<string | null>(null);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n\n      // Update active section based on scroll position\n      const sections = navigation.map(item => item.href.substring(1));\n      const currentSection = sections.find(section => {\n        const element = document.getElementById(section);\n        if (element) {\n          const rect = element.getBoundingClientRect();\n          return rect.top <= 100 && rect.bottom >= 100;\n        }\n        return false;\n      });\n\n      if (currentSection) {\n        setActiveSection(currentSection);\n      }\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const handleNavClick = (href: string) => {\n    if (href.startsWith('#')) {\n      // Handle anchor links\n      const targetId = href.substring(1);\n      const element = document.getElementById(targetId);\n      if (element) {\n        const headerOffset = 80;\n        const elementPosition = element.getBoundingClientRect().top;\n        const offsetPosition = elementPosition + window.pageYOffset - headerOffset;\n\n        window.scrollTo({\n          top: offsetPosition,\n          behavior: 'smooth'\n        });\n      }\n    } else {\n      // Handle regular page navigation\n      window.location.href = href;\n    }\n    setIsMobileMenuOpen(false);\n    setOpenDropdown(null);\n  };\n\n  return (\n    <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${isScrolled\n      ? 'bg-background/95 backdrop-blur-md shadow-lg border-b border-foreground/10'\n      : 'bg-background/90 backdrop-blur-sm'\n      }`}>\n      <nav className=\"container mx-auto px-4 py-3\">\n        <div className=\"flex items-center justify-between\">\n          {/* Logo */}\n          <Link\n            href=\"#home\"\n            className=\"text-2xl font-heading font-bold text-foreground hover:text-accent transition-colors duration-300 hover:scale-105 transform\"\n            onClick={(e) => {\n              e.preventDefault();\n              handleNavClick('#home');\n            }}\n          >\n            UR\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navigation.map((item) => (\n              <div key={item.name} className=\"relative group\">\n                {item.dropdown ? (\n                  <>\n                    <button\n                      onClick={() => handleNavClick(item.href)}\n                      onMouseEnter={() => setOpenDropdown(item.name)}\n                      onMouseLeave={() => setOpenDropdown(null)}\n                      className={`relative text-sm font-semibold transition-colors duration-300 py-2 px-1 flex items-center gap-1 ${activeSection === item.href.substring(1)\n                        ? 'text-accent'\n                        : 'text-foreground hover:text-accent'\n                        }`}\n                    >\n                      {item.name}\n                      <ChevronDown size={14} className={`transition-transform duration-200 ${openDropdown === item.name ? 'rotate-180' : ''\n                        }`} />\n                      <span className={`absolute bottom-0 left-0 h-0.5 bg-accent transition-all duration-300 ${activeSection === item.href.substring(1) ? 'w-full' : 'w-0 group-hover:w-full'\n                        }`} />\n                    </button>\n\n                    {/* Dropdown Menu */}\n                    <div\n                      className={`absolute top-full left-0 mt-2 w-48 bg-background/95 backdrop-blur-md border border-foreground/10 rounded-lg shadow-lg transition-all duration-200 ${openDropdown === item.name ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible -translate-y-2'\n                        }`}\n                      onMouseEnter={() => setOpenDropdown(item.name)}\n                      onMouseLeave={() => setOpenDropdown(null)}\n                    >\n                      {item.dropdown.map((dropdownItem) => (\n                        <button\n                          key={dropdownItem.name}\n                          onClick={() => handleNavClick(dropdownItem.href)}\n                          className=\"block w-full text-left px-4 py-3 text-sm text-foreground hover:text-accent hover:bg-foreground/10 transition-colors duration-200 first:rounded-t-lg last:rounded-b-lg\"\n                        >\n                          {dropdownItem.name}\n                        </button>\n                      ))}\n                    </div>\n                  </>\n                ) : (\n                  <button\n                    onClick={() => handleNavClick(item.href)}\n                    className={`relative text-sm font-semibold transition-colors duration-300 py-2 px-1 ${activeSection === item.href.substring(1)\n                      ? 'text-accent'\n                      : 'text-foreground hover:text-accent'\n                      }`}\n                  >\n                    {item.name}\n                    <span className={`absolute bottom-0 left-0 h-0.5 bg-accent transition-all duration-300 ${activeSection === item.href.substring(1) ? 'w-full' : 'w-0 group-hover:w-full'\n                      }`} />\n                  </button>\n                )}\n              </div>\n            ))}\n          </div>\n\n          {/* Mobile Menu Button */}\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            className=\"md:hidden text-foreground hover:text-accent hover:bg-foreground/10\"\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            aria-label=\"Toggle menu\"\n          >\n            {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}\n          </Button>\n        </div>\n\n        {/* Mobile Navigation */}\n        <div className={`md:hidden transition-all duration-300 overflow-hidden ${isMobileMenuOpen ? 'max-h-[500px] opacity-100' : 'max-h-0 opacity-0'\n          }`}>\n          <div className=\"py-4 space-y-2 border-t border-foreground/10 mt-4\">\n            {navigation.map((item) => (\n              <div key={item.name}>\n                <button\n                  onClick={() => handleNavClick(item.href)}\n                  className={`block w-full text-left px-4 py-3 text-sm font-semibold transition-colors duration-300 rounded-lg ${activeSection === item.href.substring(1)\n                    ? 'text-accent bg-foreground/10'\n                    : 'text-foreground hover:text-accent hover:bg-foreground/5'\n                    }`}\n                >\n                  {item.name}\n                </button>\n\n                {/* Mobile Dropdown Items */}\n                {item.dropdown && (\n                  <div className=\"ml-4 mt-1 space-y-1\">\n                    {item.dropdown.map((dropdownItem) => (\n                      <button\n                        key={dropdownItem.name}\n                        onClick={() => handleNavClick(dropdownItem.href)}\n                        className=\"block w-full text-left px-4 py-2 text-xs text-foreground/80 hover:text-accent hover:bg-foreground/5 transition-colors duration-300 rounded-lg\"\n                      >\n                        {dropdownItem.name}\n                      </button>\n                    ))}\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n        </div>\n      </nav>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;;;AALA;;;;;AAOA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAQ;IAC9B;QACE,MAAM;QACN,MAAM;QACN,UAAU;YACR;gBAAE,MAAM;gBAAmB,MAAM;YAAiB;YAClD;gBAAE,MAAM;gBAAmB,MAAM;YAAU;SAC5C;IACH;IACA;QAAE,MAAM;QAAW,MAAM;IAAW;IACpC;QACE,MAAM;QACN,MAAM;QACN,UAAU;YACR;gBAAE,MAAM;gBAAkB,MAAM;YAAS;YACzC;gBAAE,MAAM;gBAAkB,MAAM;YAAS;SAC1C;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,UAAU;YACR;gBAAE,MAAM;gBAAgB,MAAM;YAAQ;YACtC;gBAAE,MAAM;gBAAkB,MAAM;YAAQ;SACzC;IACH;IACA;QAAE,MAAM;QAAgB,MAAM;IAAgB;IAC9C;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAEc,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;iDAAe;oBACnB,cAAc,OAAO,OAAO,GAAG;oBAE/B,iDAAiD;oBACjD,MAAM,WAAW,WAAW,GAAG;kEAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,SAAS,CAAC;;oBAC5D,MAAM,iBAAiB,SAAS,IAAI;wEAAC,CAAA;4BACnC,MAAM,UAAU,SAAS,cAAc,CAAC;4BACxC,IAAI,SAAS;gCACX,MAAM,OAAO,QAAQ,qBAAqB;gCAC1C,OAAO,KAAK,GAAG,IAAI,OAAO,KAAK,MAAM,IAAI;4BAC3C;4BACA,OAAO;wBACT;;oBAEA,IAAI,gBAAgB;wBAClB,iBAAiB;oBACnB;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,MAAM,iBAAiB,CAAC;QACtB,IAAI,KAAK,UAAU,CAAC,MAAM;YACxB,sBAAsB;YACtB,MAAM,WAAW,KAAK,SAAS,CAAC;YAChC,MAAM,UAAU,SAAS,cAAc,CAAC;YACxC,IAAI,SAAS;gBACX,MAAM,eAAe;gBACrB,MAAM,kBAAkB,QAAQ,qBAAqB,GAAG,GAAG;gBAC3D,MAAM,iBAAiB,kBAAkB,OAAO,WAAW,GAAG;gBAE9D,OAAO,QAAQ,CAAC;oBACd,KAAK;oBACL,UAAU;gBACZ;YACF;QACF,OAAO;YACL,iCAAiC;YACjC,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;QACA,oBAAoB;QACpB,gBAAgB;IAClB;IAEA,qBACE,6LAAC;QAAO,WAAW,CAAC,4DAA4D,EAAE,aAC9E,8EACA,qCACA;kBACF,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;4BACV,SAAS,CAAC;gCACR,EAAE,cAAc;gCAChB,eAAe;4BACjB;sCACD;;;;;;sCAKD,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;oCAAoB,WAAU;8CAC5B,KAAK,QAAQ,iBACZ;;0DACE,6LAAC;gDACC,SAAS,IAAM,eAAe,KAAK,IAAI;gDACvC,cAAc,IAAM,gBAAgB,KAAK,IAAI;gDAC7C,cAAc,IAAM,gBAAgB;gDACpC,WAAW,CAAC,gGAAgG,EAAE,kBAAkB,KAAK,IAAI,CAAC,SAAS,CAAC,KAChJ,gBACA,qCACA;;oDAEH,KAAK,IAAI;kEACV,6LAAC,uNAAA,CAAA,cAAW;wDAAC,MAAM;wDAAI,WAAW,CAAC,kCAAkC,EAAE,iBAAiB,KAAK,IAAI,GAAG,eAAe,IAC/G;;;;;;kEACJ,6LAAC;wDAAK,WAAW,CAAC,qEAAqE,EAAE,kBAAkB,KAAK,IAAI,CAAC,SAAS,CAAC,KAAK,WAAW,0BAC3I;;;;;;;;;;;;0DAIN,6LAAC;gDACC,WAAW,CAAC,kJAAkJ,EAAE,iBAAiB,KAAK,IAAI,GAAG,sCAAsC,sCAC/N;gDACJ,cAAc,IAAM,gBAAgB,KAAK,IAAI;gDAC7C,cAAc,IAAM,gBAAgB;0DAEnC,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,6BAClB,6LAAC;wDAEC,SAAS,IAAM,eAAe,aAAa,IAAI;wDAC/C,WAAU;kEAET,aAAa,IAAI;uDAJb,aAAa,IAAI;;;;;;;;;;;qEAU9B,6LAAC;wCACC,SAAS,IAAM,eAAe,KAAK,IAAI;wCACvC,WAAW,CAAC,wEAAwE,EAAE,kBAAkB,KAAK,IAAI,CAAC,SAAS,CAAC,KACxH,gBACA,qCACA;;4CAEH,KAAK,IAAI;0DACV,6LAAC;gDAAK,WAAW,CAAC,qEAAqE,EAAE,kBAAkB,KAAK,IAAI,CAAC,SAAS,CAAC,KAAK,WAAW,0BAC3I;;;;;;;;;;;;mCA/CA,KAAK,IAAI;;;;;;;;;;sCAuDvB,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,oBAAoB,CAAC;4BACpC,cAAW;sCAEV,iCAAmB,6LAAC,+LAAA,CAAA,IAAC;gCAAC,MAAM;;;;;qDAAS,6LAAC,qMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;;;;;;;;;;;;8BAKtD,6LAAC;oBAAI,WAAW,CAAC,sDAAsD,EAAE,mBAAmB,8BAA8B,qBACtH;8BACF,cAAA,6LAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;;kDACC,6LAAC;wCACC,SAAS,IAAM,eAAe,KAAK,IAAI;wCACvC,WAAW,CAAC,iGAAiG,EAAE,kBAAkB,KAAK,IAAI,CAAC,SAAS,CAAC,KACjJ,iCACA,2DACA;kDAEH,KAAK,IAAI;;;;;;oCAIX,KAAK,QAAQ,kBACZ,6LAAC;wCAAI,WAAU;kDACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,6BAClB,6LAAC;gDAEC,SAAS,IAAM,eAAe,aAAa,IAAI;gDAC/C,WAAU;0DAET,aAAa,IAAI;+CAJb,aAAa,IAAI;;;;;;;;;;;+BAhBtB,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCjC;GAlLwB;KAAA", "debugId": null}}, {"offset": {"line": 427, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/uttam-portfolio/portfolio/src/components/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { ArrowUp, Mail, Phone, MapPin, Linkedin, Instagram, Youtube, Facebook, Heart } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\n\nexport default function Footer() {\n  const scrollToTop = () => {\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  };\n\n  const handleNavClick = (href: string) => {\n    const targetId = href.substring(1);\n    const element = document.getElementById(targetId);\n    if (element) {\n      const headerOffset = 80;\n      const elementPosition = element.getBoundingClientRect().top;\n      const offsetPosition = elementPosition + window.pageYOffset - headerOffset;\n\n      window.scrollTo({\n        top: offsetPosition,\n        behavior: 'smooth'\n      });\n    }\n  };\n\n  const navigation = [\n    { name: 'Home', href: '#home' },\n    { name: 'Work', href: '#featured-work' },\n    { name: 'Clients', href: '#clients' },\n    { name: '<PERSON><PERSON>', href: '#reels' },\n    { name: 'Blog', href: '#blog' },\n    { name: 'Testimonials', href: '#testimonials' },\n    { name: 'Contact', href: '#contact' },\n  ];\n\n  const socialLinks = [\n    { icon: Linkedin, href: 'https://www.linkedin.com/in/uttamrimal', label: 'LinkedIn' },\n    { icon: Instagram, href: 'https://www.instagram.com/uttamrimal', label: 'Instagram' },\n    { icon: Youtube, href: 'https://www.youtube.com/c/UttamRimal', label: 'YouTube' },\n    { icon: Facebook, href: 'https://www.facebook.com/uttamrimal', label: 'Facebook' },\n  ];\n\n  const contactInfo = [\n    { icon: Mail, text: '<EMAIL>', href: 'mailto:<EMAIL>' },\n    { icon: Phone, text: '+977 ************', href: 'tel:+9779840692118' },\n    { icon: MapPin, text: 'Kathmandu, Nepal', href: '#' },\n  ];\n\n  return (\n    <footer className=\"bg-portfolio-dark text-white relative\">\n      {/* Scroll to Top Button */}\n      <Button\n        onClick={scrollToTop}\n        className=\"absolute -top-6 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-portfolio-secondary hover:bg-portfolio-secondary/90 text-white rounded-full shadow-lg hover:scale-110 transition-all duration-300\"\n        aria-label=\"Scroll to top\"\n      >\n        <ArrowUp size={20} />\n      </Button>\n\n      <div className=\"container mx-auto px-4 pt-16 pb-8\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12\">\n          {/* Brand Section */}\n          <div className=\"lg:col-span-2\">\n            <Link\n              href=\"#home\"\n              className=\"text-3xl font-heading font-bold text-white hover:text-portfolio-accent transition-colors duration-300 inline-block mb-4\"\n              onClick={(e) => {\n                e.preventDefault();\n                handleNavClick('#home');\n              }}\n            >\n              Uttam Rimal\n            </Link>\n            <p className=\"text-white/80 leading-relaxed mb-6 max-w-md\">\n              Creative Video Editor & Storyteller passionate about transforming footage into compelling narratives.\n              Let's bring your vision to life through the power of visual storytelling.\n            </p>\n\n            {/* Social Links */}\n            <div className=\"flex gap-4\">\n              {socialLinks.map(({ icon: Icon, href, label }) => (\n                <a\n                  key={label}\n                  href={href}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"w-10 h-10 bg-white/10 hover:bg-portfolio-accent text-white hover:text-portfolio-dark rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110\"\n                  aria-label={label}\n                >\n                  <Icon size={18} />\n                </a>\n              ))}\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"text-lg font-heading font-semibold text-portfolio-accent mb-4\">\n              Quick Links\n            </h3>\n            <ul className=\"space-y-2\">\n              {navigation.map((item) => (\n                <li key={item.name}>\n                  <button\n                    onClick={() => handleNavClick(item.href)}\n                    className=\"text-white/80 hover:text-portfolio-accent transition-colors duration-300 text-sm\"\n                  >\n                    {item.name}\n                  </button>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <h3 className=\"text-lg font-heading font-semibold text-portfolio-accent mb-4\">\n              Get in Touch\n            </h3>\n            <ul className=\"space-y-3\">\n              {contactInfo.map((info, index) => (\n                <li key={index} className=\"flex items-center gap-3\">\n                  <info.icon size={16} className=\"text-portfolio-accent flex-shrink-0\" />\n                  {info.href !== '#' ? (\n                    <a\n                      href={info.href}\n                      className=\"text-white/80 hover:text-portfolio-accent transition-colors duration-300 text-sm\"\n                    >\n                      {info.text}\n                    </a>\n                  ) : (\n                    <span className=\"text-white/80 text-sm\">{info.text}</span>\n                  )}\n                </li>\n              ))}\n            </ul>\n\n            {/* WhatsApp CTA */}\n            <div className=\"mt-6\">\n              <a\n                href=\"https://wa.me/9779840692118\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"inline-flex items-center gap-2 bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-full text-sm font-semibold transition-all duration-300 hover:scale-105\"\n              >\n                <Phone size={16} />\n                WhatsApp\n              </a>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Section */}\n        <div className=\"border-t border-white/20 pt-8\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center gap-4\">\n            <div className=\"text-white/60 text-sm text-center md:text-left\">\n              <p className=\"flex items-center justify-center md:justify-start gap-1\">\n                © {new Date().getFullYear()} Uttam Rimal.\n              </p>\n            </div>\n\n            <Link\n              href=\"http://ashishkamat.com.np\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"inline-flex items-center gap-1 text-white/60 hover:text-portfolio-accent transition-colors duration-300 text-sm font-medium\"\n            >\n              <span>Made with</span>\n              <Heart size={14} className=\"text-red-500 fill-current heartbeat\" />\n              <span>by:</span>\n              <span className=\"font-semibold blink\">Ashish Kamat</span>\n            </Link>\n\n          </div>\n\n          {/* Additional Info */}\n          <div className=\"mt-4 text-center\">\n            <p className=\"text-white/40 text-xs\">\n              Professional video editing services • Available for freelance projects • Based in Kathmandu, Nepal\n            </p>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC;YACd,KAAK;YACL,UAAU;QACZ;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,WAAW,KAAK,SAAS,CAAC;QAChC,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,IAAI,SAAS;YACX,MAAM,eAAe;YACrB,MAAM,kBAAkB,QAAQ,qBAAqB,GAAG,GAAG;YAC3D,MAAM,iBAAiB,kBAAkB,OAAO,WAAW,GAAG;YAE9D,OAAO,QAAQ,CAAC;gBACd,KAAK;gBACL,UAAU;YACZ;QACF;IACF;IAEA,MAAM,aAAa;QACjB;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAQ,MAAM;QAAiB;QACvC;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAgB,MAAM;QAAgB;QAC9C;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IAED,MAAM,cAAc;QAClB;YAAE,MAAM,6MAAA,CAAA,WAAQ;YAAE,MAAM;YAA0C,OAAO;QAAW;QACpF;YAAE,MAAM,+MAAA,CAAA,YAAS;YAAE,MAAM;YAAwC,OAAO;QAAY;QACpF;YAAE,MAAM,2MAAA,CAAA,UAAO;YAAE,MAAM;YAAwC,OAAO;QAAU;QAChF;YAAE,MAAM,6MAAA,CAAA,WAAQ;YAAE,MAAM;YAAuC,OAAO;QAAW;KAClF;IAED,MAAM,cAAc;QAClB;YAAE,MAAM,qMAAA,CAAA,OAAI;YAAE,MAAM;YAAqB,MAAM;QAA2B;QAC1E;YAAE,MAAM,uMAAA,CAAA,QAAK;YAAE,MAAM;YAAqB,MAAM;QAAqB;QACrE;YAAE,MAAM,6MAAA,CAAA,SAAM;YAAE,MAAM;YAAoB,MAAM;QAAI;KACrD;IAED,qBACE,6LAAC;QAAO,WAAU;;0BAEhB,6LAAC,qIAAA,CAAA,SAAM;gBACL,SAAS;gBACT,WAAU;gBACV,cAAW;0BAEX,cAAA,6LAAC,+MAAA,CAAA,UAAO;oBAAC,MAAM;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,CAAC;4CACR,EAAE,cAAc;4CAChB,eAAe;wCACjB;kDACD;;;;;;kDAGD,6LAAC;wCAAE,WAAU;kDAA8C;;;;;;kDAM3D,6LAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,EAAE,MAAM,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,iBAC3C,6LAAC;gDAEC,MAAM;gDACN,QAAO;gDACP,KAAI;gDACJ,WAAU;gDACV,cAAY;0DAEZ,cAAA,6LAAC;oDAAK,MAAM;;;;;;+CAPP;;;;;;;;;;;;;;;;0CAcb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAgE;;;;;;kDAG9E,6LAAC;wCAAG,WAAU;kDACX,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;0DACC,cAAA,6LAAC;oDACC,SAAS,IAAM,eAAe,KAAK,IAAI;oDACvC,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAaxB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAgE;;;;;;kDAG9E,6LAAC;wCAAG,WAAU;kDACX,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC;gDAAe,WAAU;;kEACxB,6LAAC,KAAK,IAAI;wDAAC,MAAM;wDAAI,WAAU;;;;;;oDAC9B,KAAK,IAAI,KAAK,oBACb,6LAAC;wDACC,MAAM,KAAK,IAAI;wDACf,WAAU;kEAET,KAAK,IAAI;;;;;6EAGZ,6LAAC;wDAAK,WAAU;kEAAyB,KAAK,IAAI;;;;;;;+CAV7C;;;;;;;;;;kDAiBb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,WAAU;;8DAEV,6LAAC,uMAAA,CAAA,QAAK;oDAAC,MAAM;;;;;;gDAAM;;;;;;;;;;;;;;;;;;;;;;;;kCAQ3B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;;gDAA0D;gDAClE,IAAI,OAAO,WAAW;gDAAG;;;;;;;;;;;;kDAIhC,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,QAAO;wCACP,KAAI;wCACJ,WAAU;;0DAEV,6LAAC;0DAAK;;;;;;0DACN,6LAAC,uMAAA,CAAA,QAAK;gDAAC,MAAM;gDAAI,WAAU;;;;;;0DAC3B,6LAAC;0DAAK;;;;;;0DACN,6LAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;;;;;;;0CAM1C,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjD;KAvLwB", "debugId": null}}]}