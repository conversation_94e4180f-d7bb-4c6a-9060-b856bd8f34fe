'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { ArrowLeft, Play, X, Filter, Grid, List } from 'lucide-react';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { getYouTubeThumbnail, type Video } from '@/lib/api';

interface VideosPageClientProps {
  allVideos: Video[];
  featuredVideos: Video[];
}

export default function VideosPageClient({ allVideos, featuredVideos }: VideosPageClientProps) {
  const [selectedVideo, setSelectedVideo] = useState<string | null>(null);
  const [filteredVideos, setFilteredVideos] = useState<Video[]>(allVideos);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');

  // Get unique categories
  const categories = Array.from(new Set(allVideos.map(video => video.category).filter(Boolean)));

  const getYouTubeEmbedUrl = (videoId: string) => {
    return `https://www.youtube.com/embed/${videoId}?autoplay=1&rel=0&modestbranding=1`;
  };

  const handleCategoryFilter = (category: string) => {
    setCategoryFilter(category);
    if (category === 'all') {
      setFilteredVideos(allVideos);
    } else {
      setFilteredVideos(allVideos.filter(video => video.category === category));
    }
  };

  return (
    <div className="min-h-screen bg-portfolio-light">
      {/* Header */}
      <div className="bg-portfolio-primary text-white py-20">
        <div className="container mx-auto px-4">
          <Link 
            href="/#featured-work"
            className="inline-flex items-center gap-2 text-portfolio-accent hover:text-white transition-colors mb-8"
          >
            <ArrowLeft size={20} />
            Back to Portfolio
          </Link>
          
          <h1 className="text-4xl md:text-5xl font-heading font-bold mb-4">
            Video Portfolio
          </h1>
          <p className="text-xl text-white/90 max-w-2xl">
            Explore my complete collection of video editing work, from creative projects to professional tutorials.
          </p>
        </div>
      </div>

      <div className="container mx-auto px-4 py-16">
        {/* Featured Videos */}
        {featuredVideos.length > 0 && (
          <section className="mb-16">
            <h2 className="text-3xl font-heading font-bold text-portfolio-primary mb-8">
              Featured Videos
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {featuredVideos.map((video) => (
                <Dialog key={video._id}>
                  <DialogTrigger asChild>
                    <div
                      onClick={() => setSelectedVideo(video.id)}
                      className="group bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105 cursor-pointer"
                    >
                      <div className="relative aspect-video overflow-hidden">
                        <Image
                          src={getYouTubeThumbnail(video.id)}
                          alt={video.title}
                          fill
                          className="object-cover transition-transform duration-500 group-hover:scale-110"
                        />
                        
                        <div className="absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-colors duration-300 flex items-center justify-center">
                          <div className="bg-white/90 backdrop-blur-sm rounded-full p-4 group-hover:scale-110 transition-transform duration-300">
                            <Play className="h-8 w-8 text-portfolio-primary ml-1" fill="currentColor" />
                          </div>
                        </div>

                        <div className="absolute top-4 left-4">
                          <Badge variant="default" className="bg-portfolio-accent text-white">
                            Featured
                          </Badge>
                        </div>

                        {video.category && (
                          <div className="absolute top-4 right-4">
                            <Badge variant="secondary" className="bg-portfolio-secondary/90 text-white">
                              {video.category}
                            </Badge>
                          </div>
                        )}
                      </div>

                      <div className="p-6">
                        <h3 className="text-xl font-heading font-semibold text-portfolio-primary mb-3 group-hover:text-portfolio-secondary transition-colors duration-300">
                          {video.title}
                        </h3>

                        {video.description && (
                          <p className="text-muted-foreground text-sm leading-relaxed mb-4 line-clamp-2">
                            {video.description}
                          </p>
                        )}

                        {video.tags && video.tags.length > 0 && (
                          <div className="flex flex-wrap gap-2">
                            {video.tags.slice(0, 3).map((tag) => (
                              <Badge key={tag} variant="outline" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                            {video.tags.length > 3 && (
                              <Badge variant="outline" className="text-xs">
                                +{video.tags.length - 3}
                              </Badge>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </DialogTrigger>

                  <DialogContent className="max-w-4xl w-full p-0">
                    <div className="relative aspect-video">
                      <button
                        onClick={() => setSelectedVideo(null)}
                        className="absolute top-4 right-4 z-10 bg-black/50 hover:bg-black/70 text-white rounded-full p-2 transition-colors"
                      >
                        <X size={20} />
                      </button>
                      
                      {selectedVideo === video.id && (
                        <iframe
                          src={getYouTubeEmbedUrl(video.id)}
                          title={video.title}
                          className="w-full h-full rounded-lg"
                          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                          allowFullScreen
                        />
                      )}
                    </div>
                  </DialogContent>
                </Dialog>
              ))}
            </div>
          </section>
        )}

        {/* Filters and View Controls */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Filter size={20} className="text-portfolio-primary" />
              <span className="font-medium text-portfolio-primary">Filter:</span>
            </div>
            <Select value={categoryFilter} onValueChange={handleCategoryFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Select category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('grid')}
            >
              <Grid size={16} />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              <List size={16} />
            </Button>
          </div>
        </div>

        {/* All Videos */}
        <section>
          <h2 className="text-3xl font-heading font-bold text-foreground mb-8">
            {categoryFilter === 'all' ? 'All Videos' : `${categoryFilter} Videos`}
            <span className="text-lg font-normal text-muted-foreground ml-2">
              ({filteredVideos.length} videos)
            </span>
          </h2>
          
          {filteredVideos.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-lg text-muted-foreground">No videos found for the selected category.</p>
            </div>
          ) : (
            <div className={viewMode === 'grid' 
              ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" 
              : "space-y-6"
            }>
              {filteredVideos.map((video) => (
                <Dialog key={video._id}>
                  <DialogTrigger asChild>
                    <div
                      onClick={() => setSelectedVideo(video.id)}
                      className={`group bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105 cursor-pointer ${
                        viewMode === 'list' ? 'flex' : ''
                      }`}
                    >
                      <div className={`relative overflow-hidden ${
                        viewMode === 'list' ? 'w-80 aspect-video' : 'aspect-video'
                      }`}>
                        <Image
                          src={getYouTubeThumbnail(video.id)}
                          alt={video.title}
                          fill
                          className="object-cover transition-transform duration-500 group-hover:scale-110"
                        />
                        
                        <div className="absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-colors duration-300 flex items-center justify-center">
                          <div className="bg-white/90 backdrop-blur-sm rounded-full p-4 group-hover:scale-110 transition-transform duration-300">
                            <Play className="h-8 w-8 text-portfolio-primary ml-1" fill="currentColor" />
                          </div>
                        </div>

                        {video.featured && (
                          <div className="absolute top-4 left-4">
                            <Badge variant="default" className="bg-portfolio-accent text-white">
                              Featured
                            </Badge>
                          </div>
                        )}

                        {video.category && (
                          <div className="absolute top-4 right-4">
                            <Badge variant="secondary" className="bg-portfolio-secondary/90 text-white">
                              {video.category}
                            </Badge>
                          </div>
                        )}
                      </div>

                      <div className="p-6 flex-1">
                        <h3 className="text-xl font-heading font-semibold text-portfolio-primary mb-3 group-hover:text-portfolio-secondary transition-colors duration-300">
                          {video.title}
                        </h3>

                        {video.description && (
                          <p className="text-muted-foreground text-sm leading-relaxed mb-4 line-clamp-3">
                            {video.description}
                          </p>
                        )}

                        {video.tags && video.tags.length > 0 && (
                          <div className="flex flex-wrap gap-2">
                            {video.tags.slice(0, viewMode === 'list' ? 5 : 3).map((tag) => (
                              <Badge key={tag} variant="outline" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                            {video.tags.length > (viewMode === 'list' ? 5 : 3) && (
                              <Badge variant="outline" className="text-xs">
                                +{video.tags.length - (viewMode === 'list' ? 5 : 3)}
                              </Badge>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </DialogTrigger>

                  <DialogContent className="max-w-4xl w-full p-0">
                    <div className="relative aspect-video">
                      <button
                        onClick={() => setSelectedVideo(null)}
                        className="absolute top-4 right-4 z-10 bg-black/50 hover:bg-black/70 text-white rounded-full p-2 transition-colors"
                      >
                        <X size={20} />
                      </button>
                      
                      {selectedVideo === video.id && (
                        <iframe
                          src={getYouTubeEmbedUrl(video.id)}
                          title={video.title}
                          className="w-full h-full rounded-lg"
                          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                          allowFullScreen
                        />
                      )}
                    </div>
                  </DialogContent>
                </Dialog>
              ))}
            </div>
          )}
        </section>
      </div>
    </div>
  );
}
