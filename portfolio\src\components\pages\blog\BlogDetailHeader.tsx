import Link from 'next/link';
import { ArrowLeft, Calendar, Clock, User, Star } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { formatDate, type BlogPost } from '@/lib/api';

interface BlogDetailHeaderProps {
  post: BlogPost;
}

export default function BlogDetailHeader({ post }: BlogDetailHeaderProps) {
  return (
    <div className="relative bg-gradient-to-br from-primary via-primary to-accent text-primary-foreground py-20 overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-10 left-10 w-20 h-20 border border-white/20 rounded-full"></div>
        <div className="absolute top-32 right-20 w-16 h-16 border border-white/20 rounded-full"></div>
        <div className="absolute bottom-20 left-1/4 w-12 h-12 border border-white/20 rounded-full"></div>
        <div className="absolute bottom-32 right-1/3 w-24 h-24 border border-white/20 rounded-full"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <Link
          href="/blog"
          className="inline-flex items-center gap-2 text-portfolio-accent hover:text-white transition-colors mb-8 group"
        >
          <ArrowLeft size={20} className="group-hover:-translate-x-1 transition-transform duration-300" />
          Back to Blog
        </Link>

        <div className="max-w-4xl">
          {/* Category and Featured Badges */}
          <div className="flex flex-wrap gap-3 mb-6">
            <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full">
              <span className="text-sm font-medium">📝 {post.category}</span>
            </div>
            {post.featured && (
              <div className="flex items-center gap-2 bg-gradient-to-r from-yellow-400 to-orange-500 px-4 py-2 rounded-full">
                <Star size={14} className="text-white" />
                <span className="text-sm font-semibold text-white">Featured</span>
              </div>
            )}
          </div>

          {/* Title */}
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-heading font-bold mb-6 leading-tight">
            {post.title}
          </h1>

          {/* Meta Information */}
          <div className="flex flex-wrap items-center gap-6 text-white/90 mb-6">
            <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm px-3 py-1 rounded-full">
              <Calendar size={16} />
              <span className="text-sm">{formatDate(post.createdAt)}</span>
            </div>
            <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm px-3 py-1 rounded-full">
              <Clock size={16} />
              <span className="text-sm">{post.readTime} min read</span>
            </div>
            <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm px-3 py-1 rounded-full">
              <User size={16} />
              <span className="text-sm">Uttam Rimal</span>
            </div>
          </div>

          {/* Excerpt */}
          <p className="text-xl md:text-2xl text-white/90 max-w-3xl leading-relaxed">
            {post.excerpt}
          </p>

          {/* Tags Preview */}
          <div className="flex flex-wrap gap-2 mt-8">
            {post.tags.slice(0, 4).map((tag) => (
              <div key={tag} className="bg-white/10 backdrop-blur-sm px-3 py-1 rounded-full">
                <span className="text-sm text-white/80">#{tag}</span>
              </div>
            ))}
            {post.tags.length > 4 && (
              <div className="bg-white/10 backdrop-blur-sm px-3 py-1 rounded-full">
                <span className="text-sm text-white/80">+{post.tags.length - 4} more</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
