'use client';

import Link from 'next/link';
import { useState } from 'react';
import { Menu, X } from 'lucide-react';

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <header className="bg-background shadow-sm sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="text-2xl font-heading font-bold text-portfolio-primary">
            Uttam Rimal
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link href="/" className="text-portfolio-text hover:text-portfolio-primary transition-colors">
              Home
            </Link>
            <Link href="/videos" className="text-portfolio-text hover:text-portfolio-primary transition-colors">
              Videos
            </Link>
            <Link href="/reels" className="text-portfolio-text hover:text-portfolio-primary transition-colors">
              Reels
            </Link>
            <Link href="/clients" className="text-portfolio-text hover:text-portfolio-primary transition-colors">
              Clients
            </Link>
            <Link href="/blog" className="text-portfolio-text hover:text-portfolio-primary transition-colors">
              Blog
            </Link>
            <Link href="/#contact" className="bg-portfolio-primary text-white px-4 py-2 rounded-lg hover:bg-portfolio-primary/90 transition-colors">
              Contact
            </Link>
          </nav>

          {/* Mobile Menu Button */}
          <button
            onClick={toggleMenu}
            className="md:hidden p-2 text-portfolio-text hover:text-portfolio-primary"
          >
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <nav className="md:hidden py-4 border-t border-gray-200">
            <div className="flex flex-col space-y-4">
              <Link href="/" className="text-portfolio-text hover:text-portfolio-primary transition-colors">
                Home
              </Link>
              <Link href="/videos" className="text-portfolio-text hover:text-portfolio-primary transition-colors">
                Videos
              </Link>
              <Link href="/reels" className="text-portfolio-text hover:text-portfolio-primary transition-colors">
                Reels
              </Link>
              <Link href="/clients" className="text-portfolio-text hover:text-portfolio-primary transition-colors">
                Clients
              </Link>
              <Link href="/blog" className="text-portfolio-text hover:text-portfolio-primary transition-colors">
                Blog
              </Link>
              <Link href="/#contact" className="bg-portfolio-primary text-white px-4 py-2 rounded-lg hover:bg-portfolio-primary/90 transition-colors inline-block text-center">
                Contact
              </Link>
            </div>
          </nav>
        )}
      </div>
    </header>
  );
}
